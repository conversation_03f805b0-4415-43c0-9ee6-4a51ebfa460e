import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from scipy.ndimage import median_filter
import mkl_fft
import math
from multiprocessing import Pool, cpu_count
import os
from PIL import Image
import re
from concurrent.futures import ThreadPoolExecutor
from tqdm import tqdm

# ================= 核心参数 =================
NUM_SAMPLES_PER_CHIRP = 2048
NUM_CHIRPS_LIST = [512, 256, 128, 64, 64]
NUM_RANGE_BINS = 4096
NUM_CHIRPS_TOTAL = 1024
FRAME_HEADER_SIZE = 1024 * 40  # bytes

# ================= 数据读取 =================
def load_complex_bin(file_path):
    """
    读取完整 bin 文件，提取和通道重排数据部分。
    文件结构：
    1. 和通道帧头（1024*40 字节）
    2. 差通道帧头（1024*40 字节）
    3. 和通道重排数据（1024*4096 个 float32）
    4. 差通道重排数据（1024*4096 个 float32）
    """
    with open(file_path, 'rb') as f:
        # 跳过两个帧头
        f.seek(FRAME_HEADER_SIZE * 2, os.SEEK_SET)

        # 读取和通道重排数据
        and_channel_data = np.fromfile(f, dtype=np.float32, count=NUM_CHIRPS_TOTAL * NUM_RANGE_BINS)

    # 转复数数据（实部+虚部交替）
    complex_data = and_channel_data[0::2] + 1j * and_channel_data[1::2]

    # 按脉冲数 reshape
    return complex_data.reshape(NUM_CHIRPS_TOTAL, NUM_SAMPLES_PER_CHIRP)

# ================= 信号处理 =================
def apply_hanning_window(data, numSamplePerChirp, numChirps):
    win_range = 0.54 - 0.46 * np.cos((2 * math.pi * np.arange(numSamplePerChirp)) / (numSamplePerChirp - 1))
    win_doppler = 0.54 - 0.46 * np.cos((2 * math.pi * np.arange(numChirps)) / (numChirps - 1))
    return data * np.outer(win_doppler, win_range)

# ================= 图像处理 =================
def save_image(data, path, dpi=300, cmap='gray', origin='upper', binary=False):
    plt.figure(figsize=(data.shape[1] / dpi, data.shape[0] / dpi), dpi=dpi)
    if binary:
        data = np.where(data > 0.5, 1, 0)
        cmap = 'gray'
    plt.imshow(data, cmap=cmap, aspect='auto', origin=origin)
    plt.axis('off')
    plt.savefig(path, bbox_inches='tight', pad_inches=0, dpi=dpi)
    plt.close()

def resize_and_save_image(img_path, size):
    with Image.open(img_path) as img:
        img = img.resize(size, Image.LANCZOS)
        img.save(img_path)

# ================= 文件处理 =================
def process_single_file(args):
    bin_file, input_dir, output_dir, numSamplePerChirp, numChirpsList = args
    bin_path = os.path.join(input_dir, bin_file)
    base_name = os.path.splitext(bin_file)[0]

    try:
        data = load_complex_bin(bin_path)
        idx = 0
        for i, chirps in enumerate(numChirpsList):
            segment = data[idx:idx + chirps]
            idx += chirps
            if segment.shape[0] != chirps:
                print(f"[跳过] {bin_file} 的第 {i+1} 段 chirp 数不足，预期 {chirps}，实际 {segment.shape[0]}")
                continue

            windowed = apply_hanning_window(segment, numSamplePerChirp, chirps)
            rd_spectrum = mkl_fft.fft(windowed, axis=0)
            rd_log = np.log10(np.abs(rd_spectrum) + 1e-10)

            raw_path = os.path.join(output_dir, f"{base_name}_part_{i+1}.png")
            save_image(rd_log, raw_path, cmap='viridis')
            resize_and_save_image(raw_path, (numSamplePerChirp, chirps))

    except Exception as e:
        print(f"[错误] 处理文件 {bin_file} 失败: {e}")

# ================= 目录与图像合并 =================
def find_all_reordered_dirs(root_dir, subfolder_name="重排"):
    reordered_dirs = []
    for dirpath, dirnames, _ in os.walk(root_dir):
        if subfolder_name in dirnames:
            reordered_path = os.path.join(dirpath, subfolder_name)
            if os.path.isdir(reordered_path):
                reordered_dirs.append(reordered_path)
    return reordered_dirs

def extract_number_from_filename(filename):
    match = re.search(r'(\d+)', filename)
    return int(match.group(1)) if match else 0

def get_interval_folder(value, step=3):
    lower = (value // step) * step
    upper = lower + step
    return f"{lower}_{upper}"

def create_interval_folders(base_folder):
    for i in range(0, 60, 3):
        folder_name = f"{i}_{i+3}"
        os.makedirs(os.path.join(base_folder, folder_name), exist_ok=True)

def combine_images_for_base(base, input_folder, output_folder, categories, num_parts):
    for suffix in categories:
        image_list = []
        for part in range(1, num_parts + 1):
            path = os.path.join(input_folder, f"{base}{suffix}_part_{part}.png")
            if not os.path.exists(path):
                return  # 缺失直接跳过

            img = Image.open(path)
            image_list.append(img)

        widths, heights = zip(*(img.size for img in image_list))
        if len(set(widths)) > 1:
            print(f"[警告] {base} 图像宽度不一致，跳过拼接")
            return

        combined_img = Image.new('RGB', (widths[0], sum(heights)))
        y = 0
        for img in image_list:
            combined_img.paste(img, (0, y))
            y += img.height

        try:
            parts = base.split('_')
            if len(parts) >= 3:
                num = int(parts[2]) // 100
                folder = get_interval_folder(num)
                save_dir = os.path.join(output_folder, folder)
            else:
                save_dir = output_folder
        except Exception as e:
            print(f"[错误] 文件名解析失败: {base}, {e}")
            save_dir = output_folder

        os.makedirs(save_dir, exist_ok=True)
        combined_img.save(os.path.join(save_dir, f"{base}{suffix}_combined.png"))

def combine_images_in_dir(visual_dir):
    merged_dir = os.path.join(visual_dir, "合并")
    vis_dir = os.path.join(visual_dir, "可视化")
    if not os.path.exists(vis_dir):
        print(f"[跳过] 可视化目录不存在: {vis_dir}")
        return

    os.makedirs(merged_dir, exist_ok=True)
    create_interval_folders(merged_dir)

    all_files = os.listdir(vis_dir)
    base_names = {
        filename.split('_part_', 1)[0]
        for filename in all_files if '_part_' in filename and filename.endswith('.png')
    }

    base_names = sorted(base_names, key=extract_number_from_filename)
    categories = ['']
    num_parts = 5

    with ThreadPoolExecutor() as executor:
        list(tqdm(executor.map(
            lambda base: combine_images_for_base(base, vis_dir, merged_dir, categories, num_parts),
            base_names
        ), total=len(base_names), desc="拼接图像"))

# ================= 主程序入口 =================
def main(input_root):
    print("🔍 搜索包含“重排”子目录的数据...")
    reordered_dirs = find_all_reordered_dirs(input_root)
    if not reordered_dirs:
        print("❌ 未找到任何重排目录")
        return

    args_list = []
    for reordered_dir in reordered_dirs:
        output_dir = os.path.join(os.path.dirname(reordered_dir), "可视化")
        os.makedirs(output_dir, exist_ok=True)
        bin_files = [f for f in os.listdir(reordered_dir) if f.endswith('.bin')]
        for f in bin_files:
            args_list.append((f, reordered_dir, output_dir, NUM_SAMPLES_PER_CHIRP, NUM_CHIRPS_LIST))

    print(f"📦 共找到 {len(args_list)} 个待处理 bin 文件，开始并行处理...")
    with Pool(processes=min(cpu_count(), 8)) as pool:
        list(tqdm(pool.imap_unordered(process_single_file, args_list), total=len(args_list), desc="处理 .bin 文件"))

    print("🖼️ 开始拼接每个“可视化”目录下的图像...")
    visual_dirs = {os.path.dirname(arg[2]) for arg in args_list}
    for visual_dir in tqdm(visual_dirs, desc="拼接目录", unit="dir"):
        combine_images_in_dir(visual_dir)

    print("✅ 所有处理完成！")

if __name__ == "__main__":
    main("/home/<USER>/My_Data/bin_files/out2")