#include "KalmanFilter3D.hpp"

int KalmanFilter3D::next_id = 0;

KalmanFilter3D::KalmanFilter3D(const Eigen::Vector3f& init_pos, const Eigen::Vector3f& init_vel)
    : age(0), time_since_update(0), hit_streak(1), id(-1), id_assigned(false)
{
    float dt = 6.0f;

    F.setIdentity();
    for (int i = 0; i < 3; ++i)
        F(i, i + 3) = dt;

    // H.setZero();
    // H.block<3, 3>(0, 0) = Eigen::Matrix3f::Identity();

    H = Eigen::Matrix<float, 6, 6>::Identity();

    Q = Eigen::Matrix<float, 6, 6>::Identity() * 0.1f;

    // Q.block<3,3>(3,3) = Eigen::Matrix3f::Identity() * 1.0f;

    R = Eigen::Matrix3f::Identity() * 0.1f;
    P = Eigen::Matrix<float, 6, 6>::Identity() * 0.1f;

    x.setZero();
    x.block<3,1>(0,0) = init_pos;
    x.block<3,1>(3,0) = init_vel;

    history.clear();
    history.push_back(init_pos);
}

void KalmanFilter3D::predict() {
    x = F * x;
    P = F * P * F.transpose() + Q;

    history.push_back(x.block<3, 1>(0, 0));
    age++;
    time_since_update++;
}

void KalmanFilter3D::update(const Eigen::Vector3f& z_pos, const Eigen::Vector3f& z_vel) {
    Eigen::Matrix<float, 6, 1> z;
    z.block<3,1>(0,0) = z_pos;
    z.block<3,1>(3,0) = z_vel;

    Eigen::Matrix<float, 6, 6> H_full = Eigen::Matrix<float, 6, 6>::Zero();
    H_full.block<3,3>(0,0) = Eigen::Matrix3f::Identity();
    H_full.block<3,3>(3,3) = Eigen::Matrix3f::Identity();

    Eigen::Matrix<float, 6, 6> R_full = Eigen::Matrix<float, 6, 6>::Identity() * 0.1f;

    Eigen::Matrix<float, 6, 1> y = z - H_full * x;
    Eigen::Matrix<float, 6, 6> S = H_full * P * H_full.transpose() + R_full;
    Eigen::Matrix<float, 6, 6> K = P * H_full.transpose() * S.inverse();
    x = x + K * y;
    P = (Eigen::Matrix<float, 6, 6>::Identity() - K * H_full) * P;

    history.push_back(x.block<3, 1>(0, 0));
    time_since_update = 0;
    hit_streak++;
}

void KalmanFilter3D::reset(const Eigen::Vector3f& new_pos, const Eigen::Vector3f& new_vel) {
    x.setZero();
    x.block<3,1>(0,0) = new_pos;
    x.block<3,1>(3,0) = new_vel;

    P = Eigen::Matrix<float, 6, 6>::Identity() * 10.0f;
    history.clear();
    history.push_back(new_pos);

    age = 1;
    time_since_update = 0;
    hit_streak = 2;
}

Eigen::Vector3f KalmanFilter3D::getPrediction() const {
    return x.block<3,1>(0,0);
}

Eigen::Vector3f KalmanFilter3D::getVelocity() const {
    return x.block<3,1>(3,0);
}

int KalmanFilter3D::getId() const { return id; }
int KalmanFilter3D::getAge() const { return age; }
int KalmanFilter3D::getTimeSinceUpdate() const { return time_since_update; }
int KalmanFilter3D::getHitStreak() const { return hit_streak; }
bool KalmanFilter3D::hasId() const { return id_assigned; }
void KalmanFilter3D::assignId() {
    if (!id_assigned) {
        id = next_id++;
        id_assigned = true;
    }
}

std::vector<Eigen::Vector3f> KalmanFilter3D::getHistory() const {
    return history;
}

// 新增高频预测输出(仅预测位置)
Eigen::Vector3f KalmanFilter3D::predictAt(float dt) const
{
    // 临时构造一个与当前 F 相同结构的状态转移矩阵，但 dt 可变
    Eigen::Matrix<float, 6, 6> F_tmp = Eigen::Matrix<float, 6, 6>::Identity();
    for (int i = 0; i < 3; ++i) F_tmp(i, i + 3) = dt;

    Eigen::Vector<float, 6> x_tmp = F_tmp * x;
    return x_tmp.block<3, 1>(0, 0);
}