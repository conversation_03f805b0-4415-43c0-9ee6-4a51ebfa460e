#include "preprocess.hpp"
#include "postprocess.hpp"
#include "infer_engine.hpp"
#include "utils.hpp"
#include "PointTracker.hpp"
#include "logger.hpp"

#include <cuda_runtime_api.h>
#include <opencv2/opencv.hpp>
#include <chrono>
#include <iostream>
#include <fstream>
#include <iomanip>

// ====================== 主程序入口 ======================
int main() {
    initLogger();

    std::string mode;
    spdlog::info("选择运行模式 (1: 正常模式, 2: 模拟数据测试模式): ");
    std::getline(std::cin, mode);

    bool use_simulation = (mode == "2");

    if (use_simulation) {
        spdlog::info("=== 模拟数据测试模式 ===");

        std::vector<Point> all_detections = loadSimulationDataFromCSV(
            "/home/<USER>/My_Project/MSHNet_TensorRT_Test/data/radar_simulation_data.csv");

        if (all_detections.empty()) {
            spdlog::error("无法读取模拟数据，退出测试模式");
            return -1;
        }

        std::vector<Point> current_group_detections;
        int group_start_frame = -1;
        const int FRAMES_PER_GROUP = 121;

        PointTracker tracker(3, 20, 50.0f, 2);
        // std::vector<TargetInfo> all_tracked_results;

        int group_count = 0;
        for (const auto& detection : all_detections) {
            if (group_start_frame == -1) {
                group_start_frame = detection.frame;
                current_group_detections.clear();
            }

            int frame_diff = detection.frame - group_start_frame;
            if (frame_diff >= 0 && frame_diff < FRAMES_PER_GROUP) {
                current_group_detections.push_back(detection);
            } else if (frame_diff >= FRAMES_PER_GROUP) {
                if (!current_group_detections.empty()) {
                    group_count++;
                    spdlog::info("\n=== 处理第 {} 组数据 ===", group_count);
                    spdlog::info("组内检测点数量: {}", current_group_detections.size());
                    spdlog::info("起始帧号: {}, 结束帧号: {}", group_start_frame, group_start_frame + FRAMES_PER_GROUP - 1);

                    auto clustered = clusterDetections(current_group_detections, 10.0f);
                    auto tracks = tracker.update(clustered);

                    spdlog::info("聚类后点数量: {}", clustered.size());
                    spdlog::info("跟踪结果数量: {}", tracks.size());

                    auto tracked_infos = convertTracksToTargetInfos(tracks);

                    // auto smooth = tracker.interpolateTracks(1.0f);
                    tracker.interpolateTracks(1.0f, [&](const std::vector<TrackResult>& current_step) {
                        // 处理当前 step 的结果
                        auto current_step_infos = convertTracksToTargetInfos(current_step);
                        // all_tracked_results.insert(all_tracked_results.end(), current_step_infos.begin(), current_step_infos.end());
                    });

                    // spdlog::info("插值后数量: {}", smooth.size());

                    // 合并输出
                    // auto all_points = tracks;
                    // all_points.insert(all_points.end(), smooth.begin(), smooth.end());
                    // spdlog::info("\n=== 合并后数量: {} ===", all_points.size());
                    // auto all_infos = convertTracksToTargetInfos(all_points);

                    // all_tracked_results.insert(all_tracked_results.end(), tracked_infos.begin(), tracked_infos.end());
                }

                group_start_frame = detection.frame;
                current_group_detections.clear();
                current_group_detections.push_back(detection);
            }
        }

        if (!current_group_detections.empty()) {
            group_count++;
            spdlog::info("\n=== 处理第 {} 组数据 ===", group_count);
            spdlog::info("组内检测点数量: {}", current_group_detections.size());
            spdlog::info("起始帧号: {}, 结束帧号: {}", group_start_frame, group_start_frame + FRAMES_PER_GROUP - 1);

            auto clustered = clusterDetections(current_group_detections, 30.0f);
            auto tracks = tracker.update(clustered);

            spdlog::info("聚类后点数量: {}", clustered.size());
            spdlog::info("跟踪结果数量: {}", tracks.size());

            auto tracked_infos = convertTracksToTargetInfos(tracks);
            // all_tracked_results.insert(all_tracked_results.end(), tracked_infos.begin(), tracked_infos.end());
        }

        spdlog::info("\n=== 模拟数据测试完成 ===");
        // spdlog::info("总跟踪结果数量: {}", all_tracked_results.size());
        return 0;
    }

    // ==================== 正常模式 =====================
    /*目标检测开始*/
    std::string engine_path, test_path, output_path;
    spdlog::info("请输入 TensorRT 模型的路径: ");
    std::getline(std::cin, engine_path);
    spdlog::info("请输入待测数据文件夹的路径: ");
    std::getline(std::cin, test_path);
    spdlog::info("请输入输出图像保存文件夹的路径: ");
    std::getline(std::cin, output_path);

    if (engine_path.empty() || test_path.empty() || output_path.empty()) {
        spdlog::warn("使用默认路径");
        engine_path = "data/trt_net_fp16_v1.trt";
        test_path = "data/test";
        output_path = "data/vision";
    }

    initializeCustomPlugins();

    nvinfer1::IRuntime* runtime = nullptr;
    nvinfer1::ICudaEngine* engine = loadEngine(engine_path, runtime);
    nvinfer1::IExecutionContext* context = engine->createExecutionContext();

    cudaStream_t stream;
    cudaStreamCreate(&stream);

    const int inputIndex = 0, outputIndex = 1;
    const auto input_dims = engine->getBindingDimensions(inputIndex);
    const auto output_dims = engine->getBindingDimensions(outputIndex);

    const int input_h = input_dims.d[2];
    const int input_w = input_dims.d[3];
    const size_t input_size = input_dims.d[4] * input_h * input_w;
    const size_t output_size = output_dims.d[2] * output_dims.d[3];

    spdlog::info("Input shape: [{}x{}x{}]", input_dims.d[1], input_dims.d[2], input_dims.d[3]);

    void* buffers[2];
    cudaMalloc(&buffers[inputIndex], input_size * sizeof(float));
    cudaMalloc(&buffers[outputIndex], output_size * sizeof(float));

    std::vector<float> input_tensor(input_size);
    std::vector<float> output_prob(output_size);
    std::vector<float> sample_input(1024 * 1024 * 2);
    auto bin_files = get_bin_files(test_path);

    std::vector<FrameHeader> S_head , D_head;
    std::vector<float> S_data, D_data;
    loadHechaTable_("data/hecha_table.csv");

    std::vector<std::vector<std::complex<float>>> S_complex, D_complex;

    std::vector<Point> current_group_detections;
    int group_start_frame = -1;
    const int FRAMES_PER_GROUP = 120;

    PointTracker tracker(3, 20, 50.0f, 2);
    float prev_azimuth = -999.0f;
    bool azimuth_unchanged = false;

    for (const auto& file : bin_files) {
        readDualFrame(file, S_head, D_head, S_data, D_data);

        auto t0 = std::chrono::high_resolution_clock::now();
        sample_and_normalize(S_data, sample_input);
        auto t1 = std::chrono::high_resolution_clock::now();

        cudaMemcpyAsync(buffers[inputIndex], sample_input.data(), sample_input.size() * sizeof(float), cudaMemcpyHostToDevice, stream);
        cudaStreamSynchronize(stream);
        auto t2 = std::chrono::high_resolution_clock::now();

        context->enqueueV2(buffers, stream, nullptr);
        cudaStreamSynchronize(stream);
        auto t3 = std::chrono::high_resolution_clock::now();

        cudaMemcpyAsync(output_prob.data(), buffers[outputIndex], output_prob.size() * sizeof(float), cudaMemcpyDeviceToHost, stream);
        cudaStreamSynchronize(stream);
        auto t4 = std::chrono::high_resolution_clock::now();

        auto centers = post_process(output_prob.data(), 512, 1024);
        auto t5 = std::chrono::high_resolution_clock::now();

        /*目标检测结束*/

        /*目标跟踪开始*/
        S_complex = convertToComplex(S_data);
        D_complex = convertToComplex(D_data);

        auto results = computeElevationAngles_(S_head, S_complex, D_complex, centers);

        if (results.empty()) {
            spdlog::warn("No results");
            continue;
        }
        spdlog::info("\n=== 处理一组数据 ===");
        spdlog::info("当前帧检测到 {} 个点迹", results.size());
        spdlog::info("{:<10} {:<10} {:<10} {:<10} {:<10} {:<10} {:<10} {:<10} {:<10} {:<10} {:<10} {:<10} {:<10}",
            "Vx(m/s)", "Vy(m/s)", "Vz(m/s)", "X(m)", "Y(m)", "Z(m)", "Velo", "Range", "Amaz", "Elev", "Frame", "X_cor", "Y_cor");

        for (const auto& [vx, vy, vz, x, y, z, fMV, fMR, fMA, fME, frame, row, col] : results) {
            spdlog::info("{:<10.2f} {:<10.2f} {:<10.2f} {:<10.2f} {:<10.2f} {:<10.2f} {:<10.2f} {:<10.2f} {:<10.2f} {:<10.2f} {:<10} {:<10} {:<10}",
                vx, vy, vz, x, y, z, fMV, fMR, fMA, fME, frame, row, col);
        }

        for (const auto& res : results) {
            Point p;
            p.position = { std::get<3>(res), std::get<4>(res), std::get<5>(res) };
            p.velocity = { std::get<0>(res), std::get<1>(res), std::get<2>(res) };
            p.type = 1;
            p.frame = std::get<10>(res);
            p.label = -1;
            current_group_detections.push_back(p);
        }

        int current_frame = std::get<10>(results[0]);
        float current_azimuth = std::get<8>(results[0]);

        azimuth_unchanged = (prev_azimuth != -999.0f) && (std::abs(current_azimuth - prev_azimuth) < 1.0f);
        prev_azimuth = current_azimuth;

        if (group_start_frame == -1) group_start_frame = current_frame;

        bool should_process_group = false;
        if (azimuth_unchanged) {
            should_process_group = true;
            spdlog::info("方位角未变化，立即处理当前组");
        } else {
            int frame_diff = current_frame - group_start_frame;
            if (frame_diff < 0) frame_diff += 65536;
            if (frame_diff >= FRAMES_PER_GROUP - 1) should_process_group = true;
        }

        if (should_process_group || &file == &bin_files.back()) {
            if (!current_group_detections.empty()) {
                spdlog::info("组内检测数量: {}", current_group_detections.size());
                spdlog::info("起始帧号: {}, 结束帧号: {}", group_start_frame, current_frame);
                auto clustered = clusterDetections(current_group_detections, 10.0f);
                auto tracks = tracker.update(clustered);
                spdlog::info("聚类后数量: {}, 跟踪结果数量: {}", clustered.size(), tracks.size());

                auto tracked_infos = convertTracksToTargetInfos(tracks);

                // 轨迹插值
                tracker.interpolateTracks(1.0f, [&](const std::vector<TrackResult>& current_step) {
                    auto current_step_infos = convertTracksToTargetInfos(current_step);
                });

                current_group_detections.clear();
                group_start_frame = azimuth_unchanged ? -1 : current_frame + 1;
            }
        }

        int t_pre = std::chrono::duration_cast<std::chrono::milliseconds>(t1 - t0).count();
        int t_gpuin = std::chrono::duration_cast<std::chrono::milliseconds>(t2 - t1).count();
        int t_inf = std::chrono::duration_cast<std::chrono::microseconds>(t3 - t2).count();
        int t_gpuout = std::chrono::duration_cast<std::chrono::milliseconds>(t4 - t3).count();
        int t_post = std::chrono::duration_cast<std::chrono::milliseconds>(t5 - t4).count();
        int total = t_pre + t_gpuin + t_gpuout + t_post + t_inf / 1000;

        spdlog::info("耗时(ms): 预处理:{} 上传:{} 推理:{}us 下载:{} 后处理:{} 总:{} (FPS:{:.2f})",
                     t_pre, t_gpuin, t_inf, t_gpuout, t_post, total, 1000.0 / total);
        
        /*目标跟踪结束*/
        
        cv::Mat press(512, 1024, CV_8U);
        binarize(output_prob.data(), press, 512, 1024);
        std::string fname = file.substr(file.find_last_of("/\\") + 1);
        fname = fname.substr(0, fname.find(".bin")) + "_Pred.png";
        std::string out_file = output_path + "/" + fname;
        spdlog::info("保存图像: {}", out_file);
        cv::imwrite(out_file, press);
    }

    cudaFree(buffers[inputIndex]);
    cudaFree(buffers[outputIndex]);
    context->destroy();
    engine->destroy();
    runtime->destroy();
    cudaStreamDestroy(stream);

    return 0;
}