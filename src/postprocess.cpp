#include "postprocess.hpp"

void binarize(const float* prob, cv::Mat& press, int rows, int cols) {
    #pragma omp parallel for
    for (int y = 0; y < rows; ++y) {
        uchar* ptr = press.ptr<uchar>(y);
        for (int x = 0; x < cols; ++x) {
            ptr[x] = (prob[y * cols + x] > 0) ? 255 : 0;
        }
    }
}

// std::vector<std::pair<float, float>> post_process(const float* prob, int rows, int cols) {
//     cv::Mat press(rows, cols, CV_8U, cv::Scalar(0));
//     binarize(prob, press, rows, cols);
//     cv::Mat labels, stats, centroids;
//     int num_labels = cv::connectedComponentsWithStats(press, labels, stats, centroids);

//     std::vector<std::pair<float, float>> result;
//     for (int i = 1; i < num_labels; ++i) {
//         if (stats.at<int>(i, cv::CC_STAT_AREA) > 1) {
//             result.emplace_back(centroids.at<double>(i, 0), centroids.at<double>(i, 1));
//         }
//     }
//     return result;
// }

std::vector<std::pair<int, int>> post_process(const float* prob, int rows, int cols) {
    cv::Mat press(rows, cols, CV_8U, cv::Scalar(0));
    binarize(prob, press, rows, cols);
    cv::Mat labels, stats, centroids;
    int num_labels = cv::connectedComponentsWithStats(press, labels, stats, centroids);

    std::vector<std::pair<int, int>> test_points;  // 改为返回 std::pair<int, int>
    for (int i = 1; i < num_labels; ++i) {
        if (stats.at<int>(i, cv::CC_STAT_AREA) > 1) {
            // 将 centroids 的 (float, float) 坐标转换为 (int, int)，并 ×2
            int x = static_cast<int>(std::round(centroids.at<double>(i, 0) * 2));
            int y = static_cast<int>(std::round(centroids.at<double>(i, 1) * 2));
            test_points.emplace_back(x, y);
        }
    }
    return test_points;
}

// std::vector<std::pair<int, int>> post_process_combined( 
//     const float* prob, 
//     int rows, 
//     int cols, 
//     float threshold, 
//     int min_area
//  ) {
//     // 直接创建二值图像
//     cv::Mat press(rows, cols, CV_8U);
//     // 优化的二值化：结合循环展开和内存访问优化
//     #pragma omp parallel for
//     for (int y = 0; y < rows; ++y) {
//         uchar* ptr = press.ptr<uchar>(y);
//         const float* prob_row = prob + y * cols;
//         // 使用指针算术减少索引计算
//         const uchar* ptr_end = ptr + cols;
//         // 循环展开8次，进一步减少循环控制开销
//         while (ptr + 8 <= ptr_end) {
//             // 批量处理8个像素
//             ptr[0] = (prob_row[0] > threshold) ? 255 : 0;
//             ptr[1] = (prob_row[1] > threshold) ? 255 : 0;
//             ptr[2] = (prob_row[2] > threshold) ? 255 : 0;
//             ptr[3] = (prob_row[3] > threshold) ? 255 : 0;
//             ptr[4] = (prob_row[4] > threshold) ? 255 : 0;
//             ptr[5] = (prob_row[5] > threshold) ? 255 : 0;
//             ptr[6] = (prob_row[6] > threshold) ? 255 : 0;
//             ptr[7] = (prob_row[7] > threshold) ? 255 : 0;
//             ptr += 8;
//             prob_row += 8;
//         }
//         // 处理剩余不足8个的像素
//         while (ptr < ptr_end) {
//             *ptr = (*prob_row > threshold) ? 255 : 0;
//             ptr++;
//             prob_row++;
//         }
//     }
//     // 使用更高效的连通区域分析参数
//     cv::Mat labels, stats, centroids;
//     int num_labels = cv::connectedComponentsWithStats(press, labels, stats, centroids, 4, CV_32S);
//     // 预分配test_points的大小
//     std::vector<std::pair<int, int>> test_points;
//     test_points.reserve(num_labels - 1);
//     // 使用指针直接访问数据，避免重复计算
//     const int* stats_ptr = stats.ptr<int>();
//     const double* centroids_ptr = centroids.ptr<double>();
//     const int stats_cols = stats.cols;
//     const int centroids_cols = centroids.cols;
//     const int area_offset = cv::CC_STAT_AREA;
//     // 优化循环：减少分支预测失败
//     int valid_count = 0;
//     for (int i = 1; i < num_labels; ++i) {
//         if (stats_ptr[i * stats_cols + area_offset] >= min_area) {
//             valid_count++;
//         }
//     }
//     // 根据有效区域数量精确分配空间
//     test_points.reserve(valid_count);
//     // 再次遍历，只处理有效区域
//     for (int i = 1; i < num_labels; ++i) {
//         const int* row_ptr = stats_ptr + i * stats_cols;
//         if (row_ptr[area_offset] >= min_area) {
//             const double* cent_row_ptr = centroids_ptr + i * centroids_cols;
//             // 使用局部变量避免重复计算
//             const int x = static_cast<int>(std::round(cent_row_ptr[0] * 2));
//             const int y = static_cast<int>(std::round(cent_row_ptr[1] * 2));
//             test_points.emplace_back(x, y);
//         }
//     }
//     return test_points;
// }


std::vector<std::pair<int, int>>
post_process_combined(const float* prob,
                      int rows,
                      int cols,
                      float threshold,
                      float max_ratio,
                      int min_area)
{
    // 1. 二值化：交给 OpenCV（内部 SIMD + OpenMP）
    cv::Mat prob_mat(rows, cols, CV_32F, const_cast<float*>(prob));
    cv::Mat press;
    cv::threshold(prob_mat, press, threshold, 255, cv::THRESH_BINARY);
    press.convertTo(press, CV_8U);   // 0/255 掩码

    // 快速统计白点比例
    int white_cnt = cv::countNonZero(press);
    float white_ratio = static_cast<float>(white_cnt) / (rows * cols);
    if (white_ratio > max_ratio) {
        // spdlog::warn("异常帧：目标过多 {:.4f}%", white_ratio * 100);
        // 返回-1,-1
        return std::vector<std::pair<int, int>>{{-1, -1}};
    }

    // 2. 连通区域 + 面积过滤一次完成
    cv::Mat labels, stats, centroids;
    int n_label = cv::connectedComponentsWithStats(press, labels, stats, centroids,
                                                   4,   // 4 连通
                                                   CV_32S);

    // 3. 预先计算最终个数，一次性分配
    const int* areas = stats.ptr<int>(0) + cv::CC_STAT_AREA;
    int valid_cnt = 0;
    for (int i = 1; i < n_label; ++i)
        valid_cnt += (areas[i] >= min_area);

    std::vector<std::pair<int, int>> pts;
    pts.reserve(valid_cnt);

    const double* cent = centroids.ptr<double>(0);
    for (int i = 1; i < n_label; ++i) {
        if (areas[i] < min_area) continue;
        pts.emplace_back(static_cast<int>(std::round(cent[i * 2]     * 2.0)),
                         static_cast<int>(std::round(cent[i * 2 + 1] * 2.0)));
    }
    return pts;
}