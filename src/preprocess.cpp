#include "preprocess.hpp"
#include <thread>
#include <algorithm>
#include <omp.h>
#include <cuda_runtime.h>
#include <stdexcept>
#include <spdlog/spdlog.h>

void sample_data(const std::vector<double>& input, std::vector<double>& output, size_t sample_size) {
    const size_t stride = sample_size * 2;
    const size_t n = input.size() / stride * sample_size;
    output.resize(n);
    const double* input_data = input.data();
    double* output_data = output.data();

    for (size_t i = 0; i < n; ++i) {
        output_data[i] = input_data[(i / sample_size) * stride + (i % sample_size)];
    }
}

void normalize_chunk(
    const std::vector<double>& input_tensor,
    std::vector<float>& output_tensor,
    size_t start,
    size_t end,
    double real_offset,
    double real_scale_inv,
    double imag_offset,
    double imag_scale_inv
) {
    for (size_t i = start; i < end; i += 2) {
        output_tensor[i]     = static_cast<float>((input_tensor[i] - real_offset) * real_scale_inv);
        output_tensor[i + 1] = static_cast<float>((input_tensor[i + 1] - imag_offset) * imag_scale_inv);
    }
}

void normalize_input_tensor_multithreaded(
    const std::vector<double>& input_tensor,
    std::vector<float>& output_tensor,
    size_t num_threads
) {
    const double real_offset = 842827867;
    const double real_scale_inv = 1.0 / 1707641336123;
    const double imag_offset = -214253964;
    const double imag_scale_inv = 1.0 / 1699396044280;

    const size_t num_elements = input_tensor.size();
    output_tensor.resize(num_elements);
    const size_t chunk_size = (num_elements + num_threads - 1) / num_threads;

    std::vector<std::thread> threads;
    for (size_t i = 0; i < num_threads; ++i) {
        size_t start = i * chunk_size;
        size_t end = std::min(start + chunk_size, num_elements);
        if (start % 2 != 0) --start;

        threads.emplace_back(normalize_chunk,
            std::ref(input_tensor), std::ref(output_tensor),
            start, end, real_offset, real_scale_inv, imag_offset, imag_scale_inv
        );
    }

    for (auto& thread : threads) thread.join();
}

// TODO：FFT处理
void sample_and_normalize(const float* in,
                            size_t total_floats,
                            std::vector<float>& out)
{
    constexpr size_t kStride = 4;
    const size_t kBlocks = total_floats / kStride;
    out.resize(kBlocks * 2);

    constexpr float real_off = 842827867.0f;
    constexpr float real_scl = 1.0f / 1707641336123.0f;
    constexpr float imag_off = -214253964.0f;
    constexpr float imag_scl = 1.0f / 1699396044280.0f;

    #pragma omp parallel for
    for (size_t blk = 0; blk < kBlocks; ++blk) {
        const size_t in_idx  = blk * kStride;
        const size_t out_idx = blk * 2;

        out[out_idx]     = (in[in_idx]     - real_off) * real_scl;
        out[out_idx + 1] = (in[in_idx + 1] - imag_off) * imag_scl;
    }
}

// RAII类用于管理CUDA内存
class CudaMemoryRAII {
private:
    void* ptr_;

public:
    explicit CudaMemoryRAII(size_t size) : ptr_(nullptr) {
        cudaError_t status = cudaMalloc(&ptr_, size);
        if (status != cudaSuccess) {
            throw std::runtime_error("CUDA malloc failed: " + std::string(cudaGetErrorString(status)));
        }
    }

    ~CudaMemoryRAII() {
        if (ptr_) {
            cudaFree(ptr_);
        }
    }

    void* get() const { return ptr_; }

    // 禁止拷贝
    CudaMemoryRAII(const CudaMemoryRAII&) = delete;
    CudaMemoryRAII& operator=(const CudaMemoryRAII&) = delete;
};

// RAII类用于管理cuFFT计划
class CufftPlanRAII {
private:
    cufftHandle plan_;

public:
    explicit CufftPlanRAII(int n, cufftType type, int batch = 1) : plan_(0) {
        cufftResult status = cufftPlan1d(&plan_, n, type, batch);
        if (status != CUFFT_SUCCESS) {
            throw std::runtime_error("cuFFT plan creation failed");
        }
    }

    ~CufftPlanRAII() {
        if (plan_) {
            cufftDestroy(plan_);
        }
    }

    cufftHandle get() const { return plan_; }

    // 禁止拷贝
    CufftPlanRAII(const CufftPlanRAII&) = delete;
    CufftPlanRAII& operator=(const CufftPlanRAII&) = delete;
};

// 新增：带cuFFT列方向处理的预处理函数
void sample_normalize_and_fft(const float* in,
                               size_t total_floats,
                               std::vector<float>& out)
{
    // 数据维度：1024行 x 2048列 x 2(复数)
    constexpr int ROWS = 1024;
    constexpr int COLS = 2048;
    constexpr int COMPLEX_COMPONENTS = 2;

    // 验证输入数据大小
    if (total_floats != ROWS * COLS * COMPLEX_COMPONENTS) {
        throw std::runtime_error("Invalid input data size. Expected: " +
                                std::to_string(ROWS * COLS * COMPLEX_COMPONENTS) +
                                ", Got: " + std::to_string(total_floats));
    }

    spdlog::debug("开始采样、归一化和FFT处理，输入数据大小: {}", total_floats);

    try {
        // 步骤1：采样和归一化（与原函数相同的逻辑）
        constexpr size_t kStride = 4;
        const size_t kBlocks = total_floats / kStride;

        // 采样后的数据大小：1024行 x 1024列 x 2(复数)
        constexpr int SAMPLED_COLS = 1024;
        const size_t sampled_size = ROWS * SAMPLED_COLS * COMPLEX_COMPONENTS;

        std::vector<float> sampled_data(sampled_size);

        constexpr float real_off = 842827867.0f;
        constexpr float real_scl = 1.0f / 1707641336123.0f;
        constexpr float imag_off = -214253964.0f;
        constexpr float imag_scl = 1.0f / 1699396044280.0f;

        #pragma omp parallel for
        for (size_t blk = 0; blk < kBlocks; ++blk) {
            const size_t in_idx  = blk * kStride;
            const size_t out_idx = blk * 2;

            sampled_data[out_idx]     = (in[in_idx]     - real_off) * real_scl;
            sampled_data[out_idx + 1] = (in[in_idx + 1] - imag_off) * imag_scl;
        }

        spdlog::debug("采样和归一化完成，采样后数据大小: {}", sampled_data.size());

        // 步骤2：使用cuFFT处理列方向数据
        // 注意：列之间的间隔为1024（采样后）

        // 分配GPU内存用于存储采样后的复数数据
        const size_t gpu_data_size = sampled_size * sizeof(float);
        CudaMemoryRAII d_input(gpu_data_size);
        CudaMemoryRAII d_output(gpu_data_size);

        // 将采样后的数据拷贝到GPU
        cudaError_t cuda_status = cudaMemcpy(
            d_input.get(),
            sampled_data.data(),
            gpu_data_size,
            cudaMemcpyHostToDevice
        );
        if (cuda_status != cudaSuccess) {
            throw std::runtime_error("Failed to copy sampled data to GPU: " +
                                    std::string(cudaGetErrorString(cuda_status)));
        }

        spdlog::debug("数据已拷贝到GPU，开始创建cuFFT计划");

        // 创建cuFFT计划用于列方向FFT
        // 每列有1024个复数点，共1024列
        CufftPlanRAII fft_plan(ROWS, CUFFT_C2C, SAMPLED_COLS);

        // 执行列方向FFT
        cufftResult fft_status = cufftExecC2C(
            fft_plan.get(),
            reinterpret_cast<cufftComplex*>(d_input.get()),
            reinterpret_cast<cufftComplex*>(d_output.get()),
            CUFFT_FORWARD
        );
        if (fft_status != CUFFT_SUCCESS) {
            throw std::runtime_error("cuFFT column-wise execution failed");
        }

        spdlog::debug("列方向FFT处理完成");

        // 将FFT结果拷贝回CPU
        out.resize(sampled_size);
        cuda_status = cudaMemcpy(
            out.data(),
            d_output.get(),
            gpu_data_size,
            cudaMemcpyDeviceToHost
        );
        if (cuda_status != cudaSuccess) {
            throw std::runtime_error("Failed to copy FFT result from GPU: " +
                                    std::string(cudaGetErrorString(cuda_status)));
        }

        spdlog::debug("FFT结果已拷贝回CPU，输出数据大小: {}", out.size());

    } catch (const std::exception& e) {
        spdlog::error("Error in sample_normalize_and_fft: {}", e.what());
        throw;
    }
}