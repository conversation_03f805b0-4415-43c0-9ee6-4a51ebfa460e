#include "infer_engine.hpp"
#include <fstream>
#include <iostream>

Logger gLogger;

void Logger::log(Severity severity, const char* msg) noexcept {
    if (severity != Severity::kINFO)
        std::cout << msg << std::endl;
}

nvinfer1::ICudaEngine* loadEngine(const std::string& engine_path, nvinfer1::IRuntime*& runtime) {
    std::ifstream file(engine_path, std::ios::binary);
    if (!file.good()) {
        throw std::runtime_error("Failed to open engine file.");
    }

    file.seekg(0, file.end);
    size_t size = file.tellg();
    file.seekg(0, file.beg);

    std::vector<char> engine_data(size);
    file.read(engine_data.data(), size);
    file.close();

    runtime = nvinfer1::createInferRuntime(gLogger);
    return runtime->deserializeCudaEngine(engine_data.data(), size);
}
