/*************************************************************
 *  fft_test.cpp
 *
 *  测试 FFTW3：
 *  1) 对 5 个独立矩阵的每一列做 FFT
 *  2) 将 5 个矩阵合并成一个大矩阵 (1024,2048) 后做 FFT
 *  3) 比较两种方法结果的一致性
 *************************************************************/
#include <fftw3.h>
#include <complex>
#include <vector>
#include <iostream>
#include <iomanip>
#include <cmath>
#include <random>

using cd = std::complex<double>;
const double PI = acos(-1.0);

// 生成随机复数
cd rand_complex(std::mt19937& gen) {
    static std::uniform_real_distribution<double> dist(-1.0, 1.0);
    return cd(dist(gen), dist(gen));
}

int main() {
    const std::vector<int> rows_list = {512, 256, 128, 64, 64};
    const int cols = 2048;
    const int total_rows = 512 + 256 + 128 + 64 + 64; // 1024

    /* ---------- 1. 生成数据 ---------- */
    std::vector<std::vector<cd>> split_data;
    std::mt19937 rng(42);
    for (int r : rows_list) {
        std::vector<cd> buf(r * cols);
        for (auto& v : buf) v = rand_complex(rng);
        split_data.emplace_back(std::move(buf));
    }

    /* ---------- 2. 合并成一个大矩阵 ---------- */
    std::vector<cd> merged(total_rows * cols);
    size_t offset = 0;
    for (const auto& buf : split_data) {
        std::copy(buf.begin(), buf.end(), merged.begin() + offset);
        offset += buf.size();
    }

    /* ---------- 3. split FFT ---------- */
    std::vector<std::vector<cd>> split_fft;
    for (const auto& buf : split_data) {
        int r = buf.size() / cols;
        std::vector<cd> fft_buf = buf; // 复制一份做 in-place FFT
        fftw_plan p = fftw_plan_dft_1d(r,
                                       reinterpret_cast<fftw_complex*>(fft_buf.data()),
                                       reinterpret_cast<fftw_complex*>(fft_buf.data()),
                                       FFTW_FORWARD, FFTW_ESTIMATE);
        // 对每一列做 FFT
        for (int c = 0; c < cols; ++c) {
            fftw_execute_dft(p,
                             reinterpret_cast<fftw_complex*>(fft_buf.data() + c * r),
                             reinterpret_cast<fftw_complex*>(fft_buf.data() + c * r));
        }
        fftw_destroy_plan(p);
        split_fft.emplace_back(std::move(fft_buf));
    }

    /* ---------- 4. merged FFT ---------- */
    std::vector<cd> merged_fft = merged;
    fftw_plan p_merge = fftw_plan_dft_1d(total_rows,
                                         reinterpret_cast<fftw_complex*>(merged_fft.data()),
                                         reinterpret_cast<fftw_complex*>(merged_fft.data()),
                                         FFTW_FORWARD, FFTW_ESTIMATE);
    for (int c = 0; c < cols; ++c) {
        fftw_execute_dft(p_merge,
                         reinterpret_cast<fftw_complex*>(merged_fft.data() + c * total_rows),
                         reinterpret_cast<fftw_complex*>(merged_fft.data() + c * total_rows));
    }
    fftw_destroy_plan(p_merge);

    // 结果一致性检查
        /* ---------- 5. 第二份数据（独立生成） ---------- */
    std::vector<std::vector<cd>> split_data2;
    for (int r : rows_list) {
        std::vector<cd> buf(r * cols);
        for (auto& v : buf) v = rand_complex(rng);   // 用新的随机数
        split_data2.emplace_back(std::move(buf));
    }

    /* 合并第二份数据 */
    std::vector<cd> merged2(total_rows * cols);
    offset = 0;
    for (const auto& buf : split_data2) {
        std::copy(buf.begin(), buf.end(), merged2.begin() + offset);
        offset += buf.size();
    }

    /* ---------- 6. 第二份数据 split FFT ---------- */
    std::vector<std::vector<cd>> split_fft2;
    for (const auto& buf : split_data2) {
        int r = buf.size() / cols;
        std::vector<cd> fft_buf = buf;
        fftw_plan p = fftw_plan_dft_1d(r,
                                       reinterpret_cast<fftw_complex*>(fft_buf.data()),
                                       reinterpret_cast<fftw_complex*>(fft_buf.data()),
                                       FFTW_FORWARD, FFTW_ESTIMATE);
        for (int c = 0; c < cols; ++c) {
            fftw_execute_dft(p,
                             reinterpret_cast<fftw_complex*>(fft_buf.data() + c * r),
                             reinterpret_cast<fftw_complex*>(fft_buf.data() + c * r));
        }
        fftw_destroy_plan(p);
        split_fft2.emplace_back(std::move(fft_buf));
    }

    /* ---------- 7. 第二份数据 merged FFT ---------- */
    std::vector<cd> merged_fft2 = merged2;
    p_merge = fftw_plan_dft_1d(total_rows,
                               reinterpret_cast<fftw_complex*>(merged_fft2.data()),
                               reinterpret_cast<fftw_complex*>(merged_fft2.data()),
                               FFTW_FORWARD, FFTW_ESTIMATE);
    for (int c = 0; c < cols; ++c) {
        fftw_execute_dft(p_merge,
                         reinterpret_cast<fftw_complex*>(merged_fft2.data() + c * total_rows),
                         reinterpret_cast<fftw_complex*>(merged_fft2.data() + c * total_rows));
    }
    fftw_destroy_plan(p_merge);

    /* ---------- 8. 随机选同一个点并比较幅度比值 ---------- */
    {
        std::mt19937 pick_rng(123);               // 固定种子，保证两次运行选到同一点
        std::uniform_int_distribution<int> row_pick(0, total_rows - 1);
        std::uniform_int_distribution<int> col_pick(0, cols - 1);

        int picked_row = row_pick(pick_rng);
        int picked_col = col_pick(pick_rng);

        // 定位到 split 块
        int block = 0, block_start = 0;
        while (block_start + rows_list[block] <= picked_row) {
            block_start += rows_list[block++];
        }
        int local_row = picked_row - block_start;

        // 第一份数据在该点的 FFT 结果
        cd split1  = split_fft[block][picked_col * rows_list[block] + local_row];
        cd merged1 = merged_fft[picked_col * total_rows + picked_row];

        // 第二份数据在该点的 FFT 结果
        cd split2  = split_fft2[block][picked_col * rows_list[block] + local_row];
        cd merged2 = merged_fft2[picked_col * total_rows + picked_row];

        double amp1_split  = std::abs(split1);
        double amp1_merged = std::abs(merged1);
        double amp2_split  = std::abs(split2);
        double amp2_merged = std::abs(merged2);

        std::cout << "\nSame random point (row = " << picked_row
                  << ", col = " << picked_col << ")\n"
                  << "  |FFT1| split  : " << amp1_split  << '\n'
                  << "  |FFT1| merged : " << amp1_merged << '\n'
                  << "  |FFT2| split  : " << amp2_split  << '\n'
                  << "  |FFT2| merged : " << amp2_merged << '\n'
                  << "  ratio split  : " << amp2_split  / amp1_split  << '\n'
                  << "  ratio merged : " << amp2_merged / amp1_merged << '\n';
    }

    std::cout << "Done." << std::endl;
    return 0;
}