#include "preprocess.hpp"
#include <iostream>
#include <vector>
#include <chrono>
#include <random>
#include <spdlog/spdlog.h>
#include <cuda_runtime.h>

// 初始化日志
void initTestLogger() {
    spdlog::set_level(spdlog::level::debug);
    spdlog::set_pattern("[%Y-%m-%d %H:%M:%S] [%l] %v");
}

// 生成测试数据
std::vector<float> generateTestData() {
    constexpr int ROWS = 1024;
    constexpr int COLS = 2048;
    constexpr int COMPLEX_COMPONENTS = 2;
    const size_t total_size = ROWS * COLS * COMPLEX_COMPONENTS;
    
    std::vector<float> data(total_size);
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<float> dis(-1000000000.0f, 1000000000.0f);
    
    for (size_t i = 0; i < total_size; ++i) {
        data[i] = dis(gen);
    }
    
    return data;
}

// 比较两个函数的输出
void compareResults(const std::vector<float>& result1, const std::vector<float>& result2) {
    if (result1.size() != result2.size()) {
        spdlog::error("结果大小不匹配: {} vs {}", result1.size(), result2.size());
        return;
    }
    
    double max_diff = 0.0;
    double avg_diff = 0.0;
    size_t diff_count = 0;
    
    for (size_t i = 0; i < result1.size(); ++i) {
        double diff = std::abs(result1[i] - result2[i]);
        if (diff > 1e-6) {
            diff_count++;
            avg_diff += diff;
            max_diff = std::max(max_diff, diff);
        }
    }
    
    if (diff_count > 0) {
        avg_diff /= diff_count;
        spdlog::info("发现 {} 个不同的值，最大差异: {:.6e}, 平均差异: {:.6e}", 
                     diff_count, max_diff, avg_diff);
    } else {
        spdlog::info("两个结果完全相同");
    }
}

int main() {
    initTestLogger();
    
    spdlog::info("=== 新预处理函数测试开始 ===");
    
    try {
        // 检查CUDA设备
        int device_count;
        cudaError_t cuda_status = cudaGetDeviceCount(&device_count);
        if (cuda_status != cudaSuccess || device_count == 0) {
            spdlog::error("没有可用的CUDA设备");
            return -1;
        }
        spdlog::info("检测到 {} 个CUDA设备", device_count);
        
        // 生成测试数据
        spdlog::info("生成测试数据...");
        auto test_data = generateTestData();
        spdlog::info("测试数据大小: {}", test_data.size());
        
        // 测试原始函数
        spdlog::info("测试原始 sample_and_normalize 函数...");
        std::vector<float> original_result;
        auto start_time = std::chrono::high_resolution_clock::now();
        sample_and_normalize(test_data.data(), test_data.size(), original_result);
        auto end_time = std::chrono::high_resolution_clock::now();
        auto original_duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        spdlog::info("原始函数执行时间: {} ms, 输出大小: {}", original_duration.count(), original_result.size());
        
        // 测试新函数
        spdlog::info("测试新的 sample_normalize_and_fft 函数...");
        std::vector<float> new_result;
        start_time = std::chrono::high_resolution_clock::now();
        sample_normalize_and_fft(test_data.data(), test_data.size(), new_result);
        end_time = std::chrono::high_resolution_clock::now();
        auto new_duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        spdlog::info("新函数执行时间: {} ms, 输出大小: {}", new_duration.count(), new_result.size());
        
        // 验证输出大小
        if (original_result.size() == new_result.size()) {
            spdlog::info("✓ 输出大小匹配: {}", new_result.size());
        } else {
            spdlog::error("✗ 输出大小不匹配: 原始={}, 新函数={}", original_result.size(), new_result.size());
        }
        
        // 性能比较
        spdlog::info("性能比较:");
        spdlog::info("  原始函数: {} ms", original_duration.count());
        spdlog::info("  新函数:   {} ms", new_duration.count());
        if (new_duration.count() > 0) {
            double speedup = static_cast<double>(original_duration.count()) / new_duration.count();
            spdlog::info("  速度比:   {:.2f}x", speedup);
        }
        
        // 验证数据完整性（检查前几个和后几个值）
        spdlog::info("数据完整性检查:");
        spdlog::info("新函数输出前5个值: [{:.6f}, {:.6f}, {:.6f}, {:.6f}, {:.6f}]",
                     new_result[0], new_result[1], new_result[2], new_result[3], new_result[4]);
        spdlog::info("新函数输出后5个值: [{:.6f}, {:.6f}, {:.6f}, {:.6f}, {:.6f}]",
                     new_result[new_result.size()-5], new_result[new_result.size()-4], 
                     new_result[new_result.size()-3], new_result[new_result.size()-2], 
                     new_result[new_result.size()-1]);
        
        spdlog::info("=== 测试完成 ===");
        return 0;
        
    } catch (const std::exception& e) {
        spdlog::error("测试过程中发生异常: {}", e.what());
        return -1;
    }
}
