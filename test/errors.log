==1316167== Memcheck, a memory error detector
==1316167== Copyright (C) 2002-2017, and GNU GPL'd, by <PERSON> et al.
==1316167== Using Valgrind-3.18.1 and LibVEX; rerun with -h for copyright info
==1316167== Command: ./test_algorithm grep -n -A8 -B2 Invalid\\|uninitialised\\|segmentation errors.log
==1316167== Parent PID: 1263301
==1316167== 
==1316167== Warning: set address range perms: large range [0x15da6000, 0x26e81000) (noaccess)
==1316167== Warning: set address range perms: large range [0x15e00000, 0x26cdb000) (defined)
==1316167== Warning: set address range perms: large range [0x2d65e000, 0x51de4000) (noaccess)
==1316167== Warning: set address range perms: large range [0x2d800000, 0x51d86000) (defined)
==1316167== Warning: noted but unhandled ioctl 0x30000001 with no size/direction hints.
==1316167==    This could cause spurious value errors to appear.
==1316167==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1316167== Warning: noted but unhandled ioctl 0x4b with no size/direction hints.
==1316167==    This could cause spurious value errors to appear.
==1316167==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1316167== Warning: noted but unhandled ioctl 0x27 with no size/direction hints.
==1316167==    This could cause spurious value errors to appear.
==1316167==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1316167== Warning: noted but unhandled ioctl 0x25 with no size/direction hints.
==1316167==    This could cause spurious value errors to appear.
==1316167==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1316167== Warning: noted but unhandled ioctl 0x17 with no size/direction hints.
==1316167==    This could cause spurious value errors to appear.
==1316167==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1316167== Warning: set address range perms: large range [0x200000000, 0x300200000) (noaccess)
==1316167== Warning: set address range perms: large range [0x5a487000, 0x7a486000) (noaccess)
==1316167== Warning: noted but unhandled ioctl 0x19 with no size/direction hints.
==1316167==    This could cause spurious value errors to appear.
==1316167==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1316167== Warning: noted but unhandled ioctl 0x49 with no size/direction hints.
==1316167==    This could cause spurious value errors to appear.
==1316167==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1316167== Warning: noted but unhandled ioctl 0x21 with no size/direction hints.
==1316167==    This could cause spurious value errors to appear.
==1316167==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1316167== Warning: noted but unhandled ioctl 0x1b with no size/direction hints.
==1316167==    This could cause spurious value errors to appear.
==1316167==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1316167== Warning: noted but unhandled ioctl 0x44 with no size/direction hints.
==1316167==    This could cause spurious value errors to appear.
==1316167==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1316167== 
==1316167== Process terminating with default action of signal 13 (SIGPIPE)
==1316167==    at 0x512F8BF: __libc_write (write.c:26)
==1316167==    by 0x512F8BF: write (write.c:24)
==1316167==    by 0x50A5EEC: _IO_file_write@@GLIBC_2.2.5 (fileops.c:1180)
==1316167==    by 0x50A79E0: new_do_write (fileops.c:448)
==1316167==    by 0x50A79E0: _IO_new_do_write (fileops.c:425)
==1316167==    by 0x50A79E0: _IO_do_write@@GLIBC_2.2.5 (fileops.c:422)
==1316167==    by 0x50A54D7: _IO_file_sync@@GLIBC_2.2.5 (fileops.c:798)
==1316167==    by 0x509A1A9: fflush (iofflush.c:40)
==1316167==    by 0x4F0AE45: std::ostream::flush() (in /usr/lib/x86_64-linux-gnu/libstdc++.so.6.0.30)
==1316167==    by 0x1141CC: PrintTrackingResults(TrackingResult const*, int) (test_algorithm.cpp:182)
==1316167==    by 0x11421F: main::{lambda(TrackingResult const*, int)#1}::operator()(TrackingResult const*, int) const (test_algorithm.cpp:287)
==1316167==    by 0x1151ED: void std::__invoke_impl<void, main::{lambda(TrackingResult const*, int)#1}&, TrackingResult const*, int>(std::__invoke_other, main::{lambda(TrackingResult const*, int)#1}&, TrackingResult const*&&, int&&) (invoke.h:61)
==1316167==    by 0x11509C: std::enable_if<is_invocable_r_v<void, main::{lambda(TrackingResult const*, int)#1}&, TrackingResult const*, int>, void>::type std::__invoke_r<void, main::{lambda(TrackingResult const*, int)#1}&, TrackingResult const*, int>(main::{lambda(TrackingResult const*, int)#1}&, TrackingResult const*&&, int&&) (invoke.h:111)
==1316167==    by 0x114F62: std::_Function_handler<void (TrackingResult const*, int), main::{lambda(TrackingResult const*, int)#1}>::_M_invoke(std::_Any_data const&, TrackingResult const*&&, int&&) (std_function.h:290)
==1316167==    by 0x4BD74AB: std::function<void (TrackingResult const*, int)>::operator()(TrackingResult const*, int) const (std_function.h:590)
==1316167== 
==1316167== HEAP SUMMARY:
==1316167==     in use at exit: 313,592,096 bytes in 155,330 blocks
==1316167==   total heap usage: 295,647 allocs, 140,317 frees, 1,249,888,142 bytes allocated
==1316167== 
==1316167== LEAK SUMMARY:
==1316167==    definitely lost: 0 bytes in 0 blocks
==1316167==    indirectly lost: 0 bytes in 0 blocks
==1316167==      possibly lost: 89,912 bytes in 899 blocks
==1316167==    still reachable: 313,502,184 bytes in 154,431 blocks
==1316167==                       of which reachable via heuristic:
==1316167==                         stdstring          : 897,183 bytes in 15,533 blocks
==1316167==         suppressed: 0 bytes in 0 blocks
==1316167== Rerun with --leak-check=full to see details of leaked memory
==1316167== 
==1316167== For lists of detected and suppressed errors, rerun with: -s
==1316167== ERROR SUMMARY: 0 errors from 0 contexts (suppressed: 0 from 0)
