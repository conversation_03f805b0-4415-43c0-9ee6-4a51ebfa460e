==1433627== Memcheck, a memory error detector
==1433627== Copyright (C) 2002-2017, and GNU GPL'd, by <PERSON> et al.
==1433627== Using Valgrind-3.18.1 and LibVEX; rerun with -h for copyright info
==1433627== Command: ./build/test_algorithm
==1433627== Parent PID: 1428931
==1433627== 
==1433627== Warning: set address range perms: large range [0x15dbe000, 0x26e99000) (noaccess)
==1433627== Warning: set address range perms: large range [0x15e00000, 0x26cdb000) (defined)
==1433627== Warning: set address range perms: large range [0x2d65e000, 0x51de4000) (noaccess)
==1433627== Warning: set address range perms: large range [0x2d800000, 0x51d86000) (defined)
==1433627== Warning: noted but unhandled ioctl 0x30000001 with no size/direction hints.
==1433627==    This could cause spurious value errors to appear.
==1433627==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1433627== Warning: noted but unhandled ioctl 0x4b with no size/direction hints.
==1433627==    This could cause spurious value errors to appear.
==1433627==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1433627== Warning: noted but unhandled ioctl 0x27 with no size/direction hints.
==1433627==    This could cause spurious value errors to appear.
==1433627==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1433627== Warning: noted but unhandled ioctl 0x25 with no size/direction hints.
==1433627==    This could cause spurious value errors to appear.
==1433627==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1433627== Warning: noted but unhandled ioctl 0x17 with no size/direction hints.
==1433627==    This could cause spurious value errors to appear.
==1433627==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1433627== Warning: set address range perms: large range [0x200000000, 0x300200000) (noaccess)
==1433627== Warning: set address range perms: large range [0x5a487000, 0x7a486000) (noaccess)
==1433627== Warning: noted but unhandled ioctl 0x19 with no size/direction hints.
==1433627==    This could cause spurious value errors to appear.
==1433627==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1433627== Warning: noted but unhandled ioctl 0x49 with no size/direction hints.
==1433627==    This could cause spurious value errors to appear.
==1433627==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1433627== Warning: noted but unhandled ioctl 0x21 with no size/direction hints.
==1433627==    This could cause spurious value errors to appear.
==1433627==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1433627== Warning: noted but unhandled ioctl 0x1b with no size/direction hints.
==1433627==    This could cause spurious value errors to appear.
==1433627==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1433627== Warning: noted but unhandled ioctl 0x44 with no size/direction hints.
==1433627==    This could cause spurious value errors to appear.
==1433627==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1433627== 
==1433627== HEAP SUMMARY:
==1433627==     in use at exit: 1,428,948 bytes in 379 blocks
==1433627==   total heap usage: 296,794 allocs, 296,415 frees, 1,550,814,636 bytes allocated
==1433627== 
==1433627== 4 bytes in 1 blocks are still reachable in loss record 1 of 192
==1433627==    at 0x48487A9: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5268DFDD: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52922436: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x529276EE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52696FF1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5290E673: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52672E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 4 bytes in 1 blocks are still reachable in loss record 2 of 192
==1433627==    at 0x48487A9: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5268DFF1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52922436: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x529276EE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52696FF1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5290E673: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52672E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 4 bytes in 1 blocks are still reachable in loss record 3 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x27662275: cv::TLSData<cv::(anonymous namespace)::ThreadID>::createDataInstance() const (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27663E61: cv::TLSDataContainer::getData() const (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27664B8E: cv::utils::getThreadID() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27665C09: cv::TLSDataAccumulator<cv::utils::trace::details::TraceManagerThreadLocal>::createDataInstance() const (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27663E61: cv::TLSDataContainer::getData() const (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27608646: cv::parallel_for_(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2701C2DD: cv::threshold(cv::_InputArray const&, cv::_OutputArray const&, double, double, int) (in /usr/local/lib/libopencv_imgproc.so.4.8.1)
==1433627==    by 0x4C539C6: post_process_combined(float const*, int, int, float, float, int) (postprocess.cpp:130)
==1433627==    by 0x4BCF92C: TargetDetection(char*, char*, DetectionResult**, int*, FrameHeader_Alg const**, ColumnSegmentData**, int*) (libSCR_5000_Alg.cpp:412)
==1433627==    by 0x1148D1: main (test_algorithm.cpp:263)
==1433627== 
==1433627== 8 bytes in 1 blocks are still reachable in loss record 4 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x2784284C: ??? (in /usr/lib/x86_64-linux-gnu/libgomp.so.1.0.0)
==1433627==    by 0x278550BA: ??? (in /usr/lib/x86_64-linux-gnu/libgomp.so.1.0.0)
==1433627==    by 0x27840FE1: ??? (in /usr/lib/x86_64-linux-gnu/libgomp.so.1.0.0)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627==    by 0x4006567: call_init (dl-init.c:33)
==1433627==    by 0x4006567: _dl_init (dl-init.c:117)
==1433627==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==1433627== 
==1433627== 8 bytes in 1 blocks are still reachable in loss record 5 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x27661241: cv::details::getTlsAbstraction() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2766135B: cv::details::getTlsStorage() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2766496F: cv::TLSDataContainer::TLSDataContainer() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276684E6: cv::utils::trace::details::TraceManager::TraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27668DD9: cv::utils::trace::details::getTraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27669E30: cv::utils::trace::details::TraceManager::isActivated() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2766AE4A: cv::utils::trace::details::Region::Region(cv::utils::trace::details::Region::LocationStaticStorage const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276834E9: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27683A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x274E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2742C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627== 
==1433627== 8 bytes in 1 blocks are still reachable in loss record 6 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x27681E22: void std::vector<cv::utils::logging::LogTagManager::NamePartInfo, std::allocator<cv::utils::logging::LogTagManager::NamePartInfo> >::_M_realloc_insert<cv::utils::logging::LogTagManager::NamePartInfo>(__gnu_cxx::__normal_iterator<cv::utils::logging::LogTagManager::NamePartInfo*, std::vector<cv::utils::logging::LogTagManager::NamePartInfo, std::allocator<cv::utils::logging::LogTagManager::NamePartInfo> > >, cv::utils::logging::LogTagManager::NamePartInfo&&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27682A8D: cv::utils::logging::LogTagManager::NameTable::internal_addOrLookupNameParts(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&, std::vector<unsigned long, std::allocator<unsigned long> >&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276833AB: cv::utils::logging::LogTagManager::NameTable::addOrLookupFullName(cv::utils::logging::LogTagManager::FullNameLookupResult&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276835A0: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27683A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x274E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2742C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627==    by 0x4006567: call_init (dl-init.c:33)
==1433627==    by 0x4006567: _dl_init (dl-init.c:117)
==1433627==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==1433627== 
==1433627== 8 bytes in 1 blocks are still reachable in loss record 7 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5267303E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x66993FA: createInferRuntime_INTERNAL (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 8 bytes in 1 blocks are still reachable in loss record 8 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x52673786: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x66993FA: createInferRuntime_INTERNAL (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 8 bytes in 1 blocks are still reachable in loss record 9 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x275314E9: cv::Mat::getStdAllocator() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27531574: cv::Mat::getDefaultAllocator() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27532BB9: cv::Mat::create(int, int const*, int) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27532FA6: cv::Mat::create(int, int, int) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2701C189: cv::threshold(cv::_InputArray const&, cv::_OutputArray const&, double, double, int) (in /usr/local/lib/libopencv_imgproc.so.4.8.1)
==1433627==    by 0x4C539C6: post_process_combined(float const*, int, int, float, float, int) (postprocess.cpp:130)
==1433627==    by 0x4BCF92C: TargetDetection(char*, char*, DetectionResult**, int*, FrameHeader_Alg const**, ColumnSegmentData**, int*) (libSCR_5000_Alg.cpp:412)
==1433627==    by 0x1148D1: main (test_algorithm.cpp:263)
==1433627== 
==1433627== 12 bytes in 1 blocks are still reachable in loss record 10 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x50DB58E: strdup (strdup.c:42)
==1433627==    by 0x2774EA95: __itt_domain_create_init_3_0 (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27665FD8: cv::utils::trace::details::isITTEnabled() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2766893C: cv::utils::trace::details::TraceManager::TraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27668DD9: cv::utils::trace::details::getTraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27669E30: cv::utils::trace::details::TraceManager::isActivated() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2766AE4A: cv::utils::trace::details::Region::Region(cv::utils::trace::details::Region::LocationStaticStorage const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276834E9: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27683A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x274E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2742C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627== 
==1433627== 16 bytes in 1 blocks are still reachable in loss record 11 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x40085DB: malloc (rtld-malloc.h:56)
==1433627==    by 0x40085DB: decompose_rpath (dl-load.c:644)
==1433627==    by 0x400ABF5: cache_rpath (dl-load.c:696)
==1433627==    by 0x400ABF5: cache_rpath (dl-load.c:677)
==1433627==    by 0x400ABF5: _dl_map_object (dl-load.c:2165)
==1433627==    by 0x400E9A8: dl_open_worker_begin (dl-open.c:534)
==1433627==    by 0x51A7A97: _dl_catch_exception (dl-error-skeleton.c:208)
==1433627==    by 0x400DF99: dl_open_worker (dl-open.c:782)
==1433627==    by 0x51A7A97: _dl_catch_exception (dl-error-skeleton.c:208)
==1433627==    by 0x400E34D: _dl_open (dl-open.c:883)
==1433627==    by 0x50C363B: dlopen_doit (dlopen.c:56)
==1433627==    by 0x51A7A97: _dl_catch_exception (dl-error-skeleton.c:208)
==1433627==    by 0x51A7B62: _dl_catch_error (dl-error-skeleton.c:227)
==1433627==    by 0x50C312D: _dlerror_run (dlerror.c:138)
==1433627== 
==1433627== 16 bytes in 1 blocks are still reachable in loss record 12 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x2768385B: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x274E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2742C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627==    by 0x4006567: call_init (dl-init.c:33)
==1433627==    by 0x4006567: _dl_init (dl-init.c:117)
==1433627==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==1433627== 
==1433627== 16 bytes in 1 blocks are still reachable in loss record 13 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x276818E2: void std::vector<cv::utils::logging::LogTagManager::FullNameInfo, std::allocator<cv::utils::logging::LogTagManager::FullNameInfo> >::_M_realloc_insert<cv::utils::logging::LogTagManager::FullNameInfo>(__gnu_cxx::__normal_iterator<cv::utils::logging::LogTagManager::FullNameInfo*, std::vector<cv::utils::logging::LogTagManager::FullNameInfo, std::allocator<cv::utils::logging::LogTagManager::FullNameInfo> > >, cv::utils::logging::LogTagManager::FullNameInfo&&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2768291C: cv::utils::logging::LogTagManager::NameTable::internal_addOrLookupFullName(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276832D1: cv::utils::logging::LogTagManager::NameTable::addOrLookupFullName(cv::utils::logging::LogTagManager::FullNameLookupResult&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276835A0: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27683A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x274E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2742C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627==    by 0x4006567: call_init (dl-init.c:33)
==1433627==    by 0x4006567: _dl_init (dl-init.c:117)
==1433627==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==1433627== 
==1433627== 16 bytes in 1 blocks are still reachable in loss record 14 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5258C0C2: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526730BF: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 16 bytes in 1 blocks are still reachable in loss record 15 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5258C0C2: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673147: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 16 bytes in 1 blocks are still reachable in loss record 16 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5258C0C2: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5267316D: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 16 bytes in 1 blocks are still reachable in loss record 17 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5285FD78: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x527E94F4: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x527E9CE5: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 16 bytes in 1 blocks are still reachable in loss record 18 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x526DE9DD: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x527A182E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D99CE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x50C7AC2: start_thread (pthread_create.c:442)
==1433627==    by 0x5158A03: clone (clone.S:100)
==1433627== 
==1433627== 16 bytes in 1 blocks are still reachable in loss record 19 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x27664C59: cv::getCoreTlsData() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27648EFC: cv::theRNG() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27608623: cv::parallel_for_(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2701C2DD: cv::threshold(cv::_InputArray const&, cv::_OutputArray const&, double, double, int) (in /usr/local/lib/libopencv_imgproc.so.4.8.1)
==1433627==    by 0x4C539C6: post_process_combined(float const*, int, int, float, float, int) (postprocess.cpp:130)
==1433627==    by 0x4BCF92C: TargetDetection(char*, char*, DetectionResult**, int*, FrameHeader_Alg const**, ColumnSegmentData**, int*) (libSCR_5000_Alg.cpp:412)
==1433627==    by 0x1148D1: main (test_algorithm.cpp:263)
==1433627== 
==1433627== 16 bytes in 1 blocks are still reachable in loss record 20 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x27664BBC: cv::utils::getThreadID() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27665C09: cv::TLSDataAccumulator<cv::utils::trace::details::TraceManagerThreadLocal>::createDataInstance() const (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27663E61: cv::TLSDataContainer::getData() const (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27608646: cv::parallel_for_(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2701C2DD: cv::threshold(cv::_InputArray const&, cv::_OutputArray const&, double, double, int) (in /usr/local/lib/libopencv_imgproc.so.4.8.1)
==1433627==    by 0x4C539C6: post_process_combined(float const*, int, int, float, float, int) (postprocess.cpp:130)
==1433627==    by 0x4BCF92C: TargetDetection(char*, char*, DetectionResult**, int*, FrameHeader_Alg const**, ColumnSegmentData**, int*) (libSCR_5000_Alg.cpp:412)
==1433627==    by 0x1148D1: main (test_algorithm.cpp:263)
==1433627== 
==1433627== 24 bytes in 1 blocks are still reachable in loss record 21 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x527A114B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52674ACE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 32 bytes in 1 blocks are still reachable in loss record 22 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x4013E4D: malloc (rtld-malloc.h:56)
==1433627==    by 0x4013E4D: allocate_dtv_entry (dl-tls.c:684)
==1433627==    by 0x4013E4D: allocate_and_init (dl-tls.c:709)
==1433627==    by 0x4013E4D: tls_get_addr_tail (dl-tls.c:907)
==1433627==    by 0x401820B: __tls_get_addr (tls_get_addr.S:55)
==1433627==    by 0x55534A9C: ??? (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libnvrtc.so.11.8.89)
==1433627==    by 0x55534972: ??? (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libnvrtc.so.11.8.89)
==1433627==    by 0x55523F32: ??? (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libnvrtc.so.11.8.89)
==1433627==    by 0x544AD14D: ??? (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libnvrtc.so.11.8.89)
==1433627==    by 0x56720775: ??? (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libnvrtc.so.11.8.89)
==1433627==    by 0x543DA8DA: ??? (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libnvrtc.so.11.8.89)
==1433627==    by 0x6: ???
==1433627==    by 0x4006439: call_init.part.0 (dl-init.c:56)
==1433627==    by 0x4006567: call_init (dl-init.c:33)
==1433627==    by 0x4006567: _dl_init (dl-init.c:117)
==1433627== 
==1433627== 32 bytes in 1 blocks are still reachable in loss record 23 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x276831D4: cv::utils::logging::LogTagManager::NameTable::internal_addCrossReference(unsigned long, std::vector<unsigned long, std::allocator<unsigned long> > const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2768344F: cv::utils::logging::LogTagManager::NameTable::addOrLookupFullName(cv::utils::logging::LogTagManager::FullNameLookupResult&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276835A0: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27683A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x274E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2742C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627==    by 0x4006567: call_init (dl-init.c:33)
==1433627==    by 0x4006567: _dl_init (dl-init.c:117)
==1433627==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==1433627== 
==1433627== 32 bytes in 1 blocks are still reachable in loss record 24 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x27683215: cv::utils::logging::LogTagManager::NameTable::internal_addCrossReference(unsigned long, std::vector<unsigned long, std::allocator<unsigned long> > const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2768344F: cv::utils::logging::LogTagManager::NameTable::addOrLookupFullName(cv::utils::logging::LogTagManager::FullNameLookupResult&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276835A0: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27683A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x274E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2742C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627==    by 0x4006567: call_init (dl-init.c:33)
==1433627==    by 0x4006567: _dl_init (dl-init.c:117)
==1433627==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==1433627== 
==1433627== 32 bytes in 1 blocks are still reachable in loss record 25 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5266C72B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52672526: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 32 bytes in 1 blocks are still reachable in loss record 26 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5266C747: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52672526: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 32 bytes in 1 blocks are still reachable in loss record 27 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5290D8B2: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5267323A: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 32 bytes in 1 blocks are still reachable in loss record 28 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5277CC78: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5267325F: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 32 bytes in 1 blocks are still reachable in loss record 29 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5277CC78: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673A99: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 32 bytes in 1 blocks are still reachable in loss record 30 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5277CC78: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673ABB: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 32 bytes in 1 blocks are still reachable in loss record 31 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526CE945: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D3424: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526745CD: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 32 bytes in 1 blocks are still reachable in loss record 32 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5285EF98: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x527E9BF4: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 32 bytes in 1 blocks are still reachable in loss record 33 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76E6FC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7B97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76ECAE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76C63A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7723775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x632DDCD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6159699: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 32 bytes in 1 blocks are still reachable in loss record 34 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76E6FC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7B97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7C44: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7DD3: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76C6F72: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7707015: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7542074: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 32 bytes in 1 blocks are still reachable in loss record 35 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76E6FC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7B97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76ECAE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76C63A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7723775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x64B5A71: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x615E414: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 32 bytes in 1 blocks are still reachable in loss record 36 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76E6FC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7B97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76ECAE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76C63A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7723775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6555145: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x615935C: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 32 bytes in 1 blocks are still reachable in loss record 37 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76E6FC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7B97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76ECAE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76C63A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7723775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6808317: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6833664: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 32 bytes in 1 blocks are still reachable in loss record 38 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76E6FC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7B97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76ECAE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76C63A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7723775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x649FB87: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x615935C: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 32 bytes in 1 blocks are still reachable in loss record 39 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76E6FC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7B97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76ECAE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76C63A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7723775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x62E2CB2: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6159699: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 32 bytes in 1 blocks are still reachable in loss record 40 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76E6FC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7B97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76ECAE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76C63A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7723775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x61E1915: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x615F7E4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 32 bytes in 1 blocks are still reachable in loss record 41 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76E6FC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7B97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76ECAE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76C63A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7723775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x661BB7A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x661EBA9: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 32 bytes in 1 blocks are still reachable in loss record 42 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x27663EDA: cv::TLSDataContainer::getData() const (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27648EFC: cv::theRNG() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27608623: cv::parallel_for_(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2701C2DD: cv::threshold(cv::_InputArray const&, cv::_OutputArray const&, double, double, int) (in /usr/local/lib/libopencv_imgproc.so.4.8.1)
==1433627==    by 0x4C539C6: post_process_combined(float const*, int, int, float, float, int) (postprocess.cpp:130)
==1433627==    by 0x4BCF92C: TargetDetection(char*, char*, DetectionResult**, int*, FrameHeader_Alg const**, ColumnSegmentData**, int*) (libSCR_5000_Alg.cpp:412)
==1433627==    by 0x1148D1: main (test_algorithm.cpp:263)
==1433627== 
==1433627== 33 bytes in 1 blocks are still reachable in loss record 43 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5290DADC: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5267323A: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 35 bytes in 1 blocks are still reachable in loss record 44 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x40271DF: malloc (rtld-malloc.h:56)
==1433627==    by 0x40271DF: strdup (strdup.c:42)
==1433627==    by 0x4016A66: _dl_load_cache_lookup (dl-cache.c:527)
==1433627==    by 0x400A981: _dl_map_object (dl-load.c:2193)
==1433627==    by 0x400E9A8: dl_open_worker_begin (dl-open.c:534)
==1433627==    by 0x51A7A97: _dl_catch_exception (dl-error-skeleton.c:208)
==1433627==    by 0x400DF99: dl_open_worker (dl-open.c:782)
==1433627==    by 0x51A7A97: _dl_catch_exception (dl-error-skeleton.c:208)
==1433627==    by 0x400E34D: _dl_open (dl-open.c:883)
==1433627==    by 0x50C363B: dlopen_doit (dlopen.c:56)
==1433627==    by 0x51A7A97: _dl_catch_exception (dl-error-skeleton.c:208)
==1433627==    by 0x51A7B62: _dl_catch_error (dl-error-skeleton.c:227)
==1433627== 
==1433627== 39 bytes in 1 blocks are still reachable in loss record 45 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x4007173: malloc (rtld-malloc.h:56)
==1433627==    by 0x4007173: open_path (dl-load.c:1977)
==1433627==    by 0x400A6B0: _dl_map_object (dl-load.c:2158)
==1433627==    by 0x400E9A8: dl_open_worker_begin (dl-open.c:534)
==1433627==    by 0x51A7A97: _dl_catch_exception (dl-error-skeleton.c:208)
==1433627==    by 0x400DF99: dl_open_worker (dl-open.c:782)
==1433627==    by 0x51A7A97: _dl_catch_exception (dl-error-skeleton.c:208)
==1433627==    by 0x400E34D: _dl_open (dl-open.c:883)
==1433627==    by 0x50C363B: dlopen_doit (dlopen.c:56)
==1433627==    by 0x51A7A97: _dl_catch_exception (dl-error-skeleton.c:208)
==1433627==    by 0x51A7B62: _dl_catch_error (dl-error-skeleton.c:227)
==1433627==    by 0x50C312D: _dlerror_run (dlerror.c:138)
==1433627== 
==1433627== 40 bytes in 1 blocks are still reachable in loss record 46 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x2765DCC5: cv::getInitializationMutex() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27665F70: cv::utils::trace::details::isITTEnabled() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2766893C: cv::utils::trace::details::TraceManager::TraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27668DD9: cv::utils::trace::details::getTraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27669E30: cv::utils::trace::details::TraceManager::isActivated() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2766AE4A: cv::utils::trace::details::Region::Region(cv::utils::trace::details::Region::LocationStaticStorage const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276834E9: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27683A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x274E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2742C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627== 
==1433627== 40 bytes in 1 blocks are still reachable in loss record 47 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x2765D961: cv::TLSData<cv::CoreTLSData>::createDataInstance() const (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27663E61: cv::TLSDataContainer::getData() const (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27648EFC: cv::theRNG() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27608623: cv::parallel_for_(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2701C2DD: cv::threshold(cv::_InputArray const&, cv::_OutputArray const&, double, double, int) (in /usr/local/lib/libopencv_imgproc.so.4.8.1)
==1433627==    by 0x4C539C6: post_process_combined(float const*, int, int, float, float, int) (postprocess.cpp:130)
==1433627==    by 0x4BCF92C: TargetDetection(char*, char*, DetectionResult**, int*, FrameHeader_Alg const**, ColumnSegmentData**, int*) (libSCR_5000_Alg.cpp:412)
==1433627==    by 0x1148D1: main (test_algorithm.cpp:263)
==1433627== 
==1433627== 48 bytes in 1 blocks are still reachable in loss record 48 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x2774EA79: __itt_domain_create_init_3_0 (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27665FD8: cv::utils::trace::details::isITTEnabled() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2766893C: cv::utils::trace::details::TraceManager::TraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27668DD9: cv::utils::trace::details::getTraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27669E30: cv::utils::trace::details::TraceManager::isActivated() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2766AE4A: cv::utils::trace::details::Region::Region(cv::utils::trace::details::Region::LocationStaticStorage const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276834E9: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27683A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x274E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2742C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627== 
==1433627== 56 bytes in 1 blocks are still reachable in loss record 49 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x276825C0: std::pair<std::__detail::_Node_iterator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long>, false, true>, bool> std::_Hashtable<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long>, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::_M_emplace<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long const&>(std::integral_constant<bool, true>, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long const&) [clone .isra.0] (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276828FD: cv::utils::logging::LogTagManager::NameTable::internal_addOrLookupFullName(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276832D1: cv::utils::logging::LogTagManager::NameTable::addOrLookupFullName(cv::utils::logging::LogTagManager::FullNameLookupResult&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276835A0: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27683A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x274E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2742C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627==    by 0x4006567: call_init (dl-init.c:33)
==1433627==    by 0x4006567: _dl_init (dl-init.c:117)
==1433627==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==1433627== 
==1433627== 56 bytes in 1 blocks are still reachable in loss record 50 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x276825C0: std::pair<std::__detail::_Node_iterator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long>, false, true>, bool> std::_Hashtable<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long>, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::_M_emplace<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long const&>(std::integral_constant<bool, true>, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long const&) [clone .isra.0] (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27682A54: cv::utils::logging::LogTagManager::NameTable::internal_addOrLookupNameParts(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&, std::vector<unsigned long, std::allocator<unsigned long> >&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276833AB: cv::utils::logging::LogTagManager::NameTable::addOrLookupFullName(cv::utils::logging::LogTagManager::FullNameLookupResult&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276835A0: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27683A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x274E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2742C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627==    by 0x4006567: call_init (dl-init.c:33)
==1433627==    by 0x4006567: _dl_init (dl-init.c:117)
==1433627==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==1433627== 
==1433627== 56 bytes in 1 blocks are still reachable in loss record 51 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5266E9CB: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB264: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x66993FA: createInferRuntime_INTERNAL (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 56 bytes in 1 blocks are still reachable in loss record 52 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5266EA6E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB264: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x66993FA: createInferRuntime_INTERNAL (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 56 bytes in 1 blocks are still reachable in loss record 53 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5266EAF6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB264: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x66993FA: createInferRuntime_INTERNAL (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 56 bytes in 1 blocks are still reachable in loss record 54 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x526E1909: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52859818: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5290DF64: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52672E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 56 bytes in 1 blocks are still reachable in loss record 55 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x52672E85: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x66993FA: createInferRuntime_INTERNAL (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 56 bytes in 1 blocks are still reachable in loss record 56 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x526DCE2E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5267321F: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 56 bytes in 1 blocks are still reachable in loss record 57 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5285F1CF: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5285FBB4: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52851A5B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526735D7: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 56 bytes in 1 blocks are still reachable in loss record 58 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5285F1CF: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5285FBB4: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52851A82: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526735D7: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 56 bytes in 1 blocks are still reachable in loss record 59 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x526DCE2E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673769: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 64 bytes in 1 blocks are still reachable in loss record 60 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5286E2D0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52857D6F: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5285931A: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5290DF64: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52672E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 64 bytes in 1 blocks are still reachable in loss record 61 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5286E2D0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52857F80: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x528594B7: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5290DF64: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52672E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 64 bytes in 1 blocks are still reachable in loss record 62 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x52672F0E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x66993FA: createInferRuntime_INTERNAL (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 64 bytes in 1 blocks are still reachable in loss record 63 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x52672FFB: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x66993FA: createInferRuntime_INTERNAL (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 64 bytes in 1 blocks are still reachable in loss record 64 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x527E9E35: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 64 bytes in 1 blocks are still reachable in loss record 65 of 192
==1433627==    at 0x48487A9: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5279B343: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x527A122C: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52674ACE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 72 bytes in 1 blocks are still reachable in loss record 66 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277C66F: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52672A6E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 72 bytes in 1 blocks are still reachable in loss record 67 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5293305D: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52870574: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x528509A6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52848E6A: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52861B78: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526E177C: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52859818: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5290DF64: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52672E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 72 bytes in 1 blocks are still reachable in loss record 68 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x526DCEED: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x527A126E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52674ACE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 72 bytes in 1 blocks are still reachable in loss record 69 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x526CFFEE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76E6FC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7B97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76ECAE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76C63A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7723775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x632DDCD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6159699: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6574860: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 72 bytes in 1 blocks are still reachable in loss record 70 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x526CFFEE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76E6FC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7B97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7C44: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7DD3: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76C6F72: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7707015: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7542074: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x75405F5: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 72 bytes in 1 blocks are still reachable in loss record 71 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x526CFFEE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76E6FC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7B97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76ECAE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76C63A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7723775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x64B5A71: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x615E414: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x615E4F0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 72 bytes in 1 blocks are still reachable in loss record 72 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x526CFFEE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76E6FC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7B97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76ECAE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76C63A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7723775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6555145: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x615935C: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6570DB5: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 72 bytes in 1 blocks are still reachable in loss record 73 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x526CFFEE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76E6FC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7B97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76ECAE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76C63A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7723775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6808317: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6833664: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x67B2417: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 72 bytes in 1 blocks are still reachable in loss record 74 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x526CFFEE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76E6FC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7B97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76ECAE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76C63A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7723775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x649FB87: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x615935C: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6574860: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 72 bytes in 1 blocks are still reachable in loss record 75 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x526CFFEE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76E6FC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7B97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76ECAE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76C63A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7723775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x62E2CB2: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6159699: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6574860: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 72 bytes in 1 blocks are still reachable in loss record 76 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x526CFFEE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76E6FC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7B97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76ECAE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76C63A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7723775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x61E1915: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x615F7E4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6575DCE: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 72 bytes in 1 blocks are still reachable in loss record 77 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x526CFFEE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76E6FC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7B97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76ECAE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76C63A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7723775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x661BB7A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x661EBA9: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x661AA47: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 72 bytes in 1 blocks are possibly lost in loss record 78 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x528143B2: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526CB029: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526C6614: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673B70: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 72 bytes in 1 blocks are possibly lost in loss record 79 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x526D0F40: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D1441: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D340A: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526745CD: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 73 bytes in 1 blocks are still reachable in loss record 80 of 192
==1433627==    at 0x484DCD3: realloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5290D842: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5266C241: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52672526: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 74 bytes in 2 blocks are still reachable in loss record 81 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x400DD20: malloc (rtld-malloc.h:56)
==1433627==    by 0x400DD20: _dl_new_object (dl-object.c:199)
==1433627==    by 0x4008C82: _dl_map_object_from_fd (dl-load.c:1063)
==1433627==    by 0x400A600: _dl_map_object (dl-load.c:2327)
==1433627==    by 0x400E9A8: dl_open_worker_begin (dl-open.c:534)
==1433627==    by 0x51A7A97: _dl_catch_exception (dl-error-skeleton.c:208)
==1433627==    by 0x400DF99: dl_open_worker (dl-open.c:782)
==1433627==    by 0x51A7A97: _dl_catch_exception (dl-error-skeleton.c:208)
==1433627==    by 0x400E34D: _dl_open (dl-open.c:883)
==1433627==    by 0x50C363B: dlopen_doit (dlopen.c:56)
==1433627==    by 0x51A7A97: _dl_catch_exception (dl-error-skeleton.c:208)
==1433627==    by 0x51A7B62: _dl_catch_error (dl-error-skeleton.c:227)
==1433627== 
==1433627== 80 bytes in 1 blocks are still reachable in loss record 82 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x52862601: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52672B54: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 88 bytes in 1 blocks are still reachable in loss record 83 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x527A1163: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52674ACE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 88 bytes in 1 blocks are still reachable in loss record 84 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x526749E0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x66993FA: createInferRuntime_INTERNAL (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 96 bytes in 1 blocks are still reachable in loss record 85 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x27661321: cv::details::getTlsStorage() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2766496F: cv::TLSDataContainer::TLSDataContainer() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276684E6: cv::utils::trace::details::TraceManager::TraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27668DD9: cv::utils::trace::details::getTraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27669E30: cv::utils::trace::details::TraceManager::isActivated() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2766AE4A: cv::utils::trace::details::Region::Region(cv::utils::trace::details::Region::LocationStaticStorage const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276834E9: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27683A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x274E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2742C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627== 
==1433627== 104 bytes in 1 blocks are still reachable in loss record 86 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x2768248D: std::_Hashtable<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long>, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::_M_rehash(unsigned long, unsigned long const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276827D4: std::pair<std::__detail::_Node_iterator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long>, false, true>, bool> std::_Hashtable<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long>, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::_M_emplace<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long const&>(std::integral_constant<bool, true>, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long const&) [clone .isra.0] (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276828FD: cv::utils::logging::LogTagManager::NameTable::internal_addOrLookupFullName(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276832D1: cv::utils::logging::LogTagManager::NameTable::addOrLookupFullName(cv::utils::logging::LogTagManager::FullNameLookupResult&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276835A0: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27683A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x274E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2742C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627==    by 0x4006567: call_init (dl-init.c:33)
==1433627==    by 0x4006567: _dl_init (dl-init.c:117)
==1433627==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==1433627== 
==1433627== 104 bytes in 1 blocks are still reachable in loss record 87 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x2768248D: std::_Hashtable<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long>, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::_M_rehash(unsigned long, unsigned long const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276827D4: std::pair<std::__detail::_Node_iterator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long>, false, true>, bool> std::_Hashtable<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long>, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::_M_emplace<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long const&>(std::integral_constant<bool, true>, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long const&) [clone .isra.0] (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27682A54: cv::utils::logging::LogTagManager::NameTable::internal_addOrLookupNameParts(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&, std::vector<unsigned long, std::allocator<unsigned long> >&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276833AB: cv::utils::logging::LogTagManager::NameTable::addOrLookupFullName(cv::utils::logging::LogTagManager::FullNameLookupResult&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276835A0: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27683A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x274E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2742C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627==    by 0x4006567: call_init (dl-init.c:33)
==1433627==    by 0x4006567: _dl_init (dl-init.c:117)
==1433627==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==1433627== 
==1433627== 104 bytes in 1 blocks are still reachable in loss record 88 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x27682F2C: std::_Hashtable<unsigned long, std::pair<unsigned long const, std::pair<unsigned long, unsigned long> >, std::allocator<std::pair<unsigned long const, std::pair<unsigned long, unsigned long> > >, std::__detail::_Select1st, std::equal_to<unsigned long>, std::hash<unsigned long>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, false> >::_M_insert_multi_node(std::__detail::_Hash_node<std::pair<unsigned long const, std::pair<unsigned long, unsigned long> >, false>*, unsigned long, std::__detail::_Hash_node<std::pair<unsigned long const, std::pair<unsigned long, unsigned long> >, false>*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276831FD: cv::utils::logging::LogTagManager::NameTable::internal_addCrossReference(unsigned long, std::vector<unsigned long, std::allocator<unsigned long> > const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2768344F: cv::utils::logging::LogTagManager::NameTable::addOrLookupFullName(cv::utils::logging::LogTagManager::FullNameLookupResult&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276835A0: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27683A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x274E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2742C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627==    by 0x4006567: call_init (dl-init.c:33)
==1433627==    by 0x4006567: _dl_init (dl-init.c:117)
==1433627==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==1433627== 
==1433627== 104 bytes in 1 blocks are still reachable in loss record 89 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x27682F2C: std::_Hashtable<unsigned long, std::pair<unsigned long const, std::pair<unsigned long, unsigned long> >, std::allocator<std::pair<unsigned long const, std::pair<unsigned long, unsigned long> > >, std::__detail::_Select1st, std::equal_to<unsigned long>, std::hash<unsigned long>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, false> >::_M_insert_multi_node(std::__detail::_Hash_node<std::pair<unsigned long const, std::pair<unsigned long, unsigned long> >, false>*, unsigned long, std::__detail::_Hash_node<std::pair<unsigned long const, std::pair<unsigned long, unsigned long> >, false>*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2768323C: cv::utils::logging::LogTagManager::NameTable::internal_addCrossReference(unsigned long, std::vector<unsigned long, std::allocator<unsigned long> > const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2768344F: cv::utils::logging::LogTagManager::NameTable::addOrLookupFullName(cv::utils::logging::LogTagManager::FullNameLookupResult&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276835A0: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27683A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x274E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2742C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627==    by 0x4006567: call_init (dl-init.c:33)
==1433627==    by 0x4006567: _dl_init (dl-init.c:117)
==1433627==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==1433627== 
==1433627== 108 bytes in 27 blocks are still reachable in loss record 90 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x27662275: cv::TLSData<cv::(anonymous namespace)::ThreadID>::createDataInstance() const (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27663E61: cv::TLSDataContainer::getData() const (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27664B8E: cv::utils::getThreadID() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2761B48E: cv::WorkerThread::thread_loop_wrapper(void*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x50C7AC2: start_thread (pthread_create.c:442)
==1433627==    by 0x5158A03: clone (clone.S:100)
==1433627== 
==1433627== 120 bytes in 1 blocks are still reachable in loss record 91 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x527E9491: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x527E9CE5: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 128 bytes in 1 blocks are still reachable in loss record 92 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x526CB932: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52672DE2: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 128 bytes in 1 blocks are still reachable in loss record 93 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526E1A48: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52859818: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5290DF64: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52672E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 128 bytes in 1 blocks are still reachable in loss record 94 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673AF8: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 128 bytes in 1 blocks are still reachable in loss record 95 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673B27: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 128 bytes in 1 blocks are still reachable in loss record 96 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5285F4A8: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5285FBB4: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52851A5B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526735D7: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 128 bytes in 1 blocks are still reachable in loss record 97 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5285F4A8: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5285FBB4: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52851A82: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526735D7: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 128 bytes in 1 blocks are still reachable in loss record 98 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673A3A: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 128 bytes in 1 blocks are still reachable in loss record 99 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673628: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 128 bytes in 1 blocks are still reachable in loss record 100 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526CE916: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D3424: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526745CD: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 128 bytes in 1 blocks are still reachable in loss record 101 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526CE934: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D3424: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526745CD: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 128 bytes in 1 blocks are still reachable in loss record 102 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673C59: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 128 bytes in 1 blocks are still reachable in loss record 103 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673D00: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 128 bytes in 1 blocks are still reachable in loss record 104 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x527E9B2E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 128 bytes in 1 blocks are still reachable in loss record 105 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x527E9B4E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 128 bytes in 1 blocks are still reachable in loss record 106 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x527E9B6E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 128 bytes in 1 blocks are still reachable in loss record 107 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x527E9B8B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 128 bytes in 1 blocks are still reachable in loss record 108 of 192
==1433627==    at 0x48487A9: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5279B327: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x527A122C: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52674ACE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 128 bytes in 1 blocks are still reachable in loss record 109 of 192
==1433627==    at 0x48487A9: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5279B35D: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x527A122C: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52674ACE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 152 bytes in 1 blocks are still reachable in loss record 110 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673628: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 152 bytes in 1 blocks are still reachable in loss record 111 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277DFC0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5277F036: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5277F0ED: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526CE975: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D3424: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526745CD: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 152 bytes in 1 blocks are still reachable in loss record 112 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277DFC0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5277EFDB: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5277F0ED: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526CE975: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D3424: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526745CD: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 152 bytes in 1 blocks are still reachable in loss record 113 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x527E9B8B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 152 bytes in 1 blocks are still reachable in loss record 114 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277DFC0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5277F036: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5277F0ED: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x527E9519: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x527E9CE5: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 152 bytes in 1 blocks are still reachable in loss record 115 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277DFC0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5277EFDB: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5277F0ED: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x527E9519: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x527E9CE5: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 152 bytes in 1 blocks are possibly lost in loss record 116 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526E1A48: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52859818: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5290DF64: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52672E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 152 bytes in 1 blocks are possibly lost in loss record 117 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673AF8: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 152 bytes in 1 blocks are possibly lost in loss record 118 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673B27: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 152 bytes in 1 blocks are possibly lost in loss record 119 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5285F4A8: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5285FBB4: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52851A5B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526735D7: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 152 bytes in 1 blocks are possibly lost in loss record 120 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5285F4A8: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5285FBB4: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52851A82: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526735D7: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 152 bytes in 1 blocks are possibly lost in loss record 121 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673A3A: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 152 bytes in 1 blocks are possibly lost in loss record 122 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526CE916: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D3424: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526745CD: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 152 bytes in 1 blocks are possibly lost in loss record 123 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526CE934: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D3424: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526745CD: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 152 bytes in 1 blocks are possibly lost in loss record 124 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673C59: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 152 bytes in 1 blocks are possibly lost in loss record 125 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673D00: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 152 bytes in 1 blocks are possibly lost in loss record 126 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x527E9B2E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 152 bytes in 1 blocks are possibly lost in loss record 127 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x527E9B4E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 152 bytes in 1 blocks are possibly lost in loss record 128 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x527E9B6E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 160 bytes in 1 blocks are still reachable in loss record 129 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBD1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5277CC78: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5267325F: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 176 bytes in 1 blocks are still reachable in loss record 130 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x2761DE34: cv::parallel_for_pthreads(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276087C3: cv::parallel_for_(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2701C2DD: cv::threshold(cv::_InputArray const&, cv::_OutputArray const&, double, double, int) (in /usr/local/lib/libopencv_imgproc.so.4.8.1)
==1433627==    by 0x4C539C6: post_process_combined(float const*, int, int, float, float, int) (postprocess.cpp:130)
==1433627==    by 0x4BCF92C: TargetDetection(char*, char*, DetectionResult**, int*, FrameHeader_Alg const**, ColumnSegmentData**, int*) (libSCR_5000_Alg.cpp:412)
==1433627==    by 0x1148D1: main (test_algorithm.cpp:263)
==1433627== 
==1433627== 184 bytes in 1 blocks are still reachable in loss record 131 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x276839D7: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x274E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2742C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627==    by 0x4006567: call_init (dl-init.c:33)
==1433627==    by 0x4006567: _dl_init (dl-init.c:117)
==1433627==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==1433627== 
==1433627== 192 bytes in 1 blocks are still reachable in loss record 132 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x13AAA294: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==1433627==    by 0x13AF68B0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==1433627==    by 0x13A7F065: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627==    by 0x4006567: call_init (dl-init.c:33)
==1433627==    by 0x4006567: _dl_init (dl-init.c:117)
==1433627==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==1433627== 
==1433627== 192 bytes in 1 blocks are still reachable in loss record 133 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x13AAA294: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==1433627==    by 0x13AF6C30: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==1433627==    by 0x13A7F0A6: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627==    by 0x4006567: call_init (dl-init.c:33)
==1433627==    by 0x4006567: _dl_init (dl-init.c:117)
==1433627==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==1433627== 
==1433627== 192 bytes in 1 blocks are still reachable in loss record 134 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x13AAA294: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==1433627==    by 0x13AFAED0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==1433627==    by 0x13A7F205: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627==    by 0x4006567: call_init (dl-init.c:33)
==1433627==    by 0x4006567: _dl_init (dl-init.c:117)
==1433627==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==1433627== 
==1433627== 192 bytes in 1 blocks are still reachable in loss record 135 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x13AAA294: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==1433627==    by 0x13AFB250: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==1433627==    by 0x13A7F246: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627==    by 0x4006567: call_init (dl-init.c:33)
==1433627==    by 0x4006567: _dl_init (dl-init.c:117)
==1433627==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==1433627== 
==1433627== 192 bytes in 1 blocks are still reachable in loss record 136 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x2784284C: ??? (in /usr/lib/x86_64-linux-gnu/libgomp.so.1.0.0)
==1433627==    by 0x27852E0B: ??? (in /usr/lib/x86_64-linux-gnu/libgomp.so.1.0.0)
==1433627==    by 0x278499F9: GOMP_parallel (in /usr/lib/x86_64-linux-gnu/libgomp.so.1.0.0)
==1433627==    by 0x4C55243: sample_and_normalize(float const*, unsigned long, std::vector<float, std::allocator<float> >&) (preprocess.cpp:76)
==1433627==    by 0x4BCF4A0: TargetDetection(char*, char*, DetectionResult**, int*, FrameHeader_Alg const**, ColumnSegmentData**, int*) (libSCR_5000_Alg.cpp:368)
==1433627==    by 0x1148D1: main (test_algorithm.cpp:263)
==1433627== 
==1433627== 224 bytes in 1 blocks are still reachable in loss record 137 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x526E1874: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52859818: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5290DF64: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52672E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 232 bytes in 1 blocks are still reachable in loss record 138 of 192
==1433627==    at 0x48487A9: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x278428AC: ??? (in /usr/lib/x86_64-linux-gnu/libgomp.so.1.0.0)
==1433627==    by 0x27853B3C: ??? (in /usr/lib/x86_64-linux-gnu/libgomp.so.1.0.0)
==1433627==    by 0x27849A10: GOMP_parallel (in /usr/lib/x86_64-linux-gnu/libgomp.so.1.0.0)
==1433627==    by 0x4C55243: sample_and_normalize(float const*, unsigned long, std::vector<float, std::allocator<float> >&) (preprocess.cpp:76)
==1433627==    by 0x4BCF4A0: TargetDetection(char*, char*, DetectionResult**, int*, FrameHeader_Alg const**, ColumnSegmentData**, int*) (libSCR_5000_Alg.cpp:368)
==1433627==    by 0x1148D1: main (test_algorithm.cpp:263)
==1433627== 
==1433627== 240 bytes in 1 blocks are still reachable in loss record 139 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x52852A46: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526DEF73: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52859818: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5290DF64: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52672E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 248 bytes in 1 blocks are still reachable in loss record 140 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5267395A: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x66993FA: createInferRuntime_INTERNAL (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 256 bytes in 1 blocks are still reachable in loss record 141 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x2766137F: cv::details::getTlsStorage() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2766496F: cv::TLSDataContainer::TLSDataContainer() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276684E6: cv::utils::trace::details::TraceManager::TraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27668DD9: cv::utils::trace::details::getTraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27669E30: cv::utils::trace::details::TraceManager::isActivated() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2766AE4A: cv::utils::trace::details::Region::Region(cv::utils::trace::details::Region::LocationStaticStorage const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276834E9: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27683A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x274E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2742C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627== 
==1433627== 256 bytes in 1 blocks are still reachable in loss record 142 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x27661404: cv::details::getTlsStorage() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2766496F: cv::TLSDataContainer::TLSDataContainer() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276684E6: cv::utils::trace::details::TraceManager::TraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27668DD9: cv::utils::trace::details::getTraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27669E30: cv::utils::trace::details::TraceManager::isActivated() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2766AE4A: cv::utils::trace::details::Region::Region(cv::utils::trace::details::Region::LocationStaticStorage const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276834E9: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27683A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x274E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2742C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627== 
==1433627== 256 bytes in 1 blocks are still reachable in loss record 143 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x27663EFE: cv::TLSDataContainer::getData() const (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27648EFC: cv::theRNG() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27608623: cv::parallel_for_(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2701C2DD: cv::threshold(cv::_InputArray const&, cv::_OutputArray const&, double, double, int) (in /usr/local/lib/libopencv_imgproc.so.4.8.1)
==1433627==    by 0x4C539C6: post_process_combined(float const*, int, int, float, float, int) (postprocess.cpp:130)
==1433627==    by 0x4BCF92C: TargetDetection(char*, char*, DetectionResult**, int*, FrameHeader_Alg const**, ColumnSegmentData**, int*) (libSCR_5000_Alg.cpp:412)
==1433627==    by 0x1148D1: main (test_algorithm.cpp:263)
==1433627== 
==1433627== 264 bytes in 1 blocks are still reachable in loss record 144 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5267327E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x66993FA: createInferRuntime_INTERNAL (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 272 bytes in 1 blocks are still reachable in loss record 145 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x527A1064: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52674ACE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 320 bytes in 1 blocks are still reachable in loss record 146 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBD1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76E6FC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7B97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76ECAE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76C63A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7723775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x632DDCD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6159699: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 320 bytes in 1 blocks are still reachable in loss record 147 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBD1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76E6FC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7B97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7C44: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7DD3: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76C6F72: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7707015: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7542074: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 320 bytes in 1 blocks are still reachable in loss record 148 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBD1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76E6FC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7B97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76ECAE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76C63A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7723775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x64B5A71: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x615E414: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 320 bytes in 1 blocks are still reachable in loss record 149 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBD1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76E6FC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7B97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76ECAE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76C63A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7723775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6555145: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x615935C: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 320 bytes in 1 blocks are still reachable in loss record 150 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBD1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76E6FC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7B97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76ECAE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76C63A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7723775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6808317: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6833664: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 320 bytes in 1 blocks are still reachable in loss record 151 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBD1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76E6FC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7B97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76ECAE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76C63A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7723775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x649FB87: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x615935C: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 320 bytes in 1 blocks are still reachable in loss record 152 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBD1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76E6FC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7B97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76ECAE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76C63A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7723775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x62E2CB2: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6159699: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 320 bytes in 1 blocks are still reachable in loss record 153 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBD1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76E6FC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7B97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76ECAE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76C63A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7723775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x61E1915: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x615F7E4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 320 bytes in 1 blocks are still reachable in loss record 154 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBD1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x525AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76E6FC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76E7B97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76ECAE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76C63A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7723775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x661BB7A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x661EBA9: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 320 bytes in 1 blocks are still reachable in loss record 155 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CCB4: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5277D9EE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52943453: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526AA8EC: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52634C64: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5274C26F: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x4C8F7DB: __cudart1628 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==1433627==    by 0x4C705F0: __cudart745 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==1433627==    by 0x4CAAEE4: cudaDeviceReset (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==1433627==    by 0x4BD2CEF: ReleaseAllResources() (libSCR_5000_Alg.cpp:856)
==1433627==    by 0x114DBD: main (test_algorithm.cpp:332)
==1433627== 
==1433627== 320 bytes in 1 blocks are still reachable in loss record 156 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CCB4: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5277D9EE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526AB38B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52634C64: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5274C26F: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x4C8F7DB: __cudart1628 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==1433627==    by 0x4C705F0: __cudart745 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==1433627==    by 0x4CAAEE4: cudaDeviceReset (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==1433627==    by 0x4BD2CEF: ReleaseAllResources() (libSCR_5000_Alg.cpp:856)
==1433627==    by 0x114DBD: main (test_algorithm.cpp:332)
==1433627== 
==1433627== 336 bytes in 1 blocks are still reachable in loss record 157 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x274E961C: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2742C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627==    by 0x4006567: call_init (dl-init.c:33)
==1433627==    by 0x4006567: _dl_init (dl-init.c:117)
==1433627==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==1433627== 
==1433627== 336 bytes in 1 blocks are still reachable in loss record 158 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x13AA1170: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==1433627==    by 0x13A7B8F5: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627==    by 0x4006567: call_init (dl-init.c:33)
==1433627==    by 0x4006567: _dl_init (dl-init.c:117)
==1433627==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==1433627== 
==1433627== 416 bytes in 1 blocks are possibly lost in loss record 159 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x40147D9: calloc (rtld-malloc.h:44)
==1433627==    by 0x40147D9: allocate_dtv (dl-tls.c:375)
==1433627==    by 0x40147D9: _dl_allocate_tls (dl-tls.c:634)
==1433627==    by 0x50C87B4: allocate_stack (allocatestack.c:430)
==1433627==    by 0x50C87B4: pthread_create@@GLIBC_2.34 (pthread_create.c:647)
==1433627==    by 0x526DCF52: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x527A126E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52674ACE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 440 bytes in 1 blocks are still reachable in loss record 160 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBD1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526CE945: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526D3424: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526745CD: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 464 bytes in 1 blocks are still reachable in loss record 161 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5290DEB2: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52672E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 512 bytes in 1 blocks are still reachable in loss record 162 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x52672F65: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x66993FA: createInferRuntime_INTERNAL (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 512 bytes in 1 blocks are still reachable in loss record 163 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x52672FB2: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x66993FA: createInferRuntime_INTERNAL (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 512 bytes in 1 blocks are still reachable in loss record 164 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x2761CBBD: void std::vector<cv::Ptr<cv::WorkerThread>, std::allocator<cv::Ptr<cv::WorkerThread> > >::_M_realloc_insert<cv::Ptr<cv::WorkerThread> >(__gnu_cxx::__normal_iterator<cv::Ptr<cv::WorkerThread>*, std::vector<cv::Ptr<cv::WorkerThread>, std::allocator<cv::Ptr<cv::WorkerThread> > > >, cv::Ptr<cv::WorkerThread>&&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2761CD1B: cv::ThreadPool::reconfigure_(unsigned int) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2761D6D4: cv::ThreadPool::run(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276087C3: cv::parallel_for_(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2701C2DD: cv::threshold(cv::_InputArray const&, cv::_OutputArray const&, double, double, int) (in /usr/local/lib/libopencv_imgproc.so.4.8.1)
==1433627==    by 0x4C539C6: post_process_combined(float const*, int, int, float, float, int) (postprocess.cpp:130)
==1433627==    by 0x4BCF92C: TargetDetection(char*, char*, DetectionResult**, int*, FrameHeader_Alg const**, ColumnSegmentData**, int*) (libSCR_5000_Alg.cpp:412)
==1433627==    by 0x1148D1: main (test_algorithm.cpp:263)
==1433627== 
==1433627== 552 bytes in 2 blocks are still reachable in loss record 165 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x40162DC: calloc (rtld-malloc.h:44)
==1433627==    by 0x40162DC: _dl_check_map_versions (dl-version.c:273)
==1433627==    by 0x400ED13: dl_open_worker_begin (dl-open.c:600)
==1433627==    by 0x51A7A97: _dl_catch_exception (dl-error-skeleton.c:208)
==1433627==    by 0x400DF99: dl_open_worker (dl-open.c:782)
==1433627==    by 0x51A7A97: _dl_catch_exception (dl-error-skeleton.c:208)
==1433627==    by 0x400E34D: _dl_open (dl-open.c:883)
==1433627==    by 0x50C363B: dlopen_doit (dlopen.c:56)
==1433627==    by 0x51A7A97: _dl_catch_exception (dl-error-skeleton.c:208)
==1433627==    by 0x51A7B62: _dl_catch_error (dl-error-skeleton.c:227)
==1433627==    by 0x50C312D: _dlerror_run (dlerror.c:138)
==1433627==    by 0x50C36C7: dlopen_implementation (dlopen.c:71)
==1433627==    by 0x50C36C7: dlopen@@GLIBC_2.34 (dlopen.c:81)
==1433627== 
==1433627== 640 bytes in 1 blocks are still reachable in loss record 166 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBD1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5277CC78: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673ABB: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 648 bytes in 27 blocks are still reachable in loss record 167 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x2761CCEC: cv::ThreadPool::reconfigure_(unsigned int) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2761D6D4: cv::ThreadPool::run(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276087C3: cv::parallel_for_(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2701C2DD: cv::threshold(cv::_InputArray const&, cv::_OutputArray const&, double, double, int) (in /usr/local/lib/libopencv_imgproc.so.4.8.1)
==1433627==    by 0x4C539C6: post_process_combined(float const*, int, int, float, float, int) (postprocess.cpp:130)
==1433627==    by 0x4BCF92C: TargetDetection(char*, char*, DetectionResult**, int*, FrameHeader_Alg const**, ColumnSegmentData**, int*) (libSCR_5000_Alg.cpp:412)
==1433627==    by 0x1148D1: main (test_algorithm.cpp:263)
==1433627== 
==1433627== 864 bytes in 27 blocks are still reachable in loss record 168 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x27663EDA: cv::TLSDataContainer::getData() const (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27664B8E: cv::utils::getThreadID() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2761B48E: cv::WorkerThread::thread_loop_wrapper(void*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x50C7AC2: start_thread (pthread_create.c:442)
==1433627==    by 0x5158A03: clone (clone.S:100)
==1433627== 
==1433627== 920 bytes in 1 blocks are still reachable in loss record 169 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x52673362: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x66993FA: createInferRuntime_INTERNAL (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 1,025 bytes in 1 blocks are still reachable in loss record 170 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5290D941: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5267323A: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 1,088 bytes in 1 blocks are still reachable in loss record 171 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x52852990: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526DEF73: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52859818: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5290DF64: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52672E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 1,280 bytes in 1 blocks are still reachable in loss record 172 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277CBD1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5277CC78: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673A99: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 1,536 bytes in 1 blocks are still reachable in loss record 173 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x13AAA294: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==1433627==    by 0x13AEE46D: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==1433627==    by 0x13A7EF55: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627==    by 0x4006567: call_init (dl-init.c:33)
==1433627==    by 0x4006567: _dl_init (dl-init.c:117)
==1433627==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==1433627== 
==1433627== 2,409 bytes in 2 blocks are still reachable in loss record 174 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x400DA02: calloc (rtld-malloc.h:44)
==1433627==    by 0x400DA02: _dl_new_object (dl-object.c:92)
==1433627==    by 0x4008C82: _dl_map_object_from_fd (dl-load.c:1063)
==1433627==    by 0x400A600: _dl_map_object (dl-load.c:2327)
==1433627==    by 0x400E9A8: dl_open_worker_begin (dl-open.c:534)
==1433627==    by 0x51A7A97: _dl_catch_exception (dl-error-skeleton.c:208)
==1433627==    by 0x400DF99: dl_open_worker (dl-open.c:782)
==1433627==    by 0x51A7A97: _dl_catch_exception (dl-error-skeleton.c:208)
==1433627==    by 0x400E34D: _dl_open (dl-open.c:883)
==1433627==    by 0x50C363B: dlopen_doit (dlopen.c:56)
==1433627==    by 0x51A7A97: _dl_catch_exception (dl-error-skeleton.c:208)
==1433627==    by 0x51A7B62: _dl_catch_error (dl-error-skeleton.c:227)
==1433627== 
==1433627== 2,576 bytes in 1 blocks are still reachable in loss record 175 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x527E9A7D: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52673D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 3,888 bytes in 27 blocks are still reachable in loss record 176 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x2761CCC3: cv::ThreadPool::reconfigure_(unsigned int) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2761D6D4: cv::ThreadPool::run(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276087C3: cv::parallel_for_(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2701C2DD: cv::threshold(cv::_InputArray const&, cv::_OutputArray const&, double, double, int) (in /usr/local/lib/libopencv_imgproc.so.4.8.1)
==1433627==    by 0x4C539C6: post_process_combined(float const*, int, int, float, float, int) (postprocess.cpp:130)
==1433627==    by 0x4BCF92C: TargetDetection(char*, char*, DetectionResult**, int*, FrameHeader_Alg const**, ColumnSegmentData**, int*) (libSCR_5000_Alg.cpp:412)
==1433627==    by 0x1148D1: main (test_algorithm.cpp:263)
==1433627== 
==1433627== 6,912 bytes in 27 blocks are still reachable in loss record 177 of 192
==1433627==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x27663EFE: cv::TLSDataContainer::getData() const (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x27664B8E: cv::utils::getThreadID() (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2761B48E: cv::WorkerThread::thread_loop_wrapper(void*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x50C7AC2: start_thread (pthread_create.c:442)
==1433627==    by 0x5158A03: clone (clone.S:100)
==1433627== 
==1433627== 7,616 bytes in 1 blocks are still reachable in loss record 178 of 192
==1433627==    at 0x484E120: memalign (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x278428E8: ??? (in /usr/lib/x86_64-linux-gnu/libgomp.so.1.0.0)
==1433627==    by 0x27852C96: ??? (in /usr/lib/x86_64-linux-gnu/libgomp.so.1.0.0)
==1433627==    by 0x278499F9: GOMP_parallel (in /usr/lib/x86_64-linux-gnu/libgomp.so.1.0.0)
==1433627==    by 0x4C55243: sample_and_normalize(float const*, unsigned long, std::vector<float, std::allocator<float> >&) (preprocess.cpp:76)
==1433627==    by 0x4BCF4A0: TargetDetection(char*, char*, DetectionResult**, int*, FrameHeader_Alg const**, ColumnSegmentData**, int*) (libSCR_5000_Alg.cpp:368)
==1433627==    by 0x1148D1: main (test_algorithm.cpp:263)
==1433627== 
==1433627== 8,192 bytes in 1 blocks are still reachable in loss record 179 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x1607020E: ??? (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libcufft.so.10.9.0.58)
==1433627==    by 0x1606AF5F: cufftCreate (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libcufft.so.10.9.0.58)
==1433627==    by 0x1606BFE3: cufftPlanMany (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libcufft.so.10.9.0.58)
==1433627==    by 0x4BCA3D7: FFTGPUOptimizer::FFTGPUOptimizer(int, int) (fft_gpu.cpp:20)
==1433627==    by 0x4BE81BF: std::_MakeUniq<FFTGPUOptimizer>::__single_object std::make_unique<FFTGPUOptimizer, int, int>(int&&, int&&) (unique_ptr.h:962)
==1433627==    by 0x4BCEF34: InitializeAlgorithmLibrary(char const*) (libSCR_5000_Alg.cpp:321)
==1433627==    by 0x114337: main (test_algorithm.cpp:200)
==1433627== 
==1433627== 11,232 bytes in 27 blocks are possibly lost in loss record 180 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x40147D9: calloc (rtld-malloc.h:44)
==1433627==    by 0x40147D9: allocate_dtv (dl-tls.c:375)
==1433627==    by 0x40147D9: _dl_allocate_tls (dl-tls.c:634)
==1433627==    by 0x50C87B4: allocate_stack (allocatestack.c:430)
==1433627==    by 0x50C87B4: pthread_create@@GLIBC_2.34 (pthread_create.c:647)
==1433627==    by 0x2785325F: ??? (in /usr/lib/x86_64-linux-gnu/libgomp.so.1.0.0)
==1433627==    by 0x27849A10: GOMP_parallel (in /usr/lib/x86_64-linux-gnu/libgomp.so.1.0.0)
==1433627==    by 0x4C55243: sample_and_normalize(float const*, unsigned long, std::vector<float, std::allocator<float> >&) (preprocess.cpp:76)
==1433627==    by 0x4BCF4A0: TargetDetection(char*, char*, DetectionResult**, int*, FrameHeader_Alg const**, ColumnSegmentData**, int*) (libSCR_5000_Alg.cpp:368)
==1433627==    by 0x1148D1: main (test_algorithm.cpp:263)
==1433627== 
==1433627== 11,232 bytes in 27 blocks are possibly lost in loss record 181 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x40147D9: calloc (rtld-malloc.h:44)
==1433627==    by 0x40147D9: allocate_dtv (dl-tls.c:375)
==1433627==    by 0x40147D9: _dl_allocate_tls (dl-tls.c:634)
==1433627==    by 0x50C87B4: allocate_stack (allocatestack.c:430)
==1433627==    by 0x50C87B4: pthread_create@@GLIBC_2.34 (pthread_create.c:647)
==1433627==    by 0x2761C2A4: cv::WorkerThread::WorkerThread(cv::ThreadPool&, unsigned int) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2761CCD4: cv::ThreadPool::reconfigure_(unsigned int) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2761D6D4: cv::ThreadPool::run(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x276087C3: cv::parallel_for_(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==1433627==    by 0x2701C2DD: cv::threshold(cv::_InputArray const&, cv::_OutputArray const&, double, double, int) (in /usr/local/lib/libopencv_imgproc.so.4.8.1)
==1433627==    by 0x4C539C6: post_process_combined(float const*, int, int, float, float, int) (postprocess.cpp:130)
==1433627==    by 0x4BCF92C: TargetDetection(char*, char*, DetectionResult**, int*, FrameHeader_Alg const**, ColumnSegmentData**, int*) (libSCR_5000_Alg.cpp:412)
==1433627==    by 0x1148D1: main (test_algorithm.cpp:263)
==1433627== 
==1433627== 16,384 bytes in 1 blocks are still reachable in loss record 182 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x52673094: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x66993FA: createInferRuntime_INTERNAL (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 16,688 bytes in 1 blocks are still reachable in loss record 183 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x526E18C8: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52859818: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5290DF64: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52672E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 17,624 bytes in 1 blocks are still reachable in loss record 184 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x526DEDDF: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52859818: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5290DF64: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52672E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 49,152 bytes in 3 blocks are still reachable in loss record 185 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x526739AA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x66993FA: createInferRuntime_INTERNAL (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 72,704 bytes in 1 blocks are still reachable in loss record 186 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x54454D95: ??? (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libnvrtc.so.11.8.89)
==1433627==    by 0x56720775: ??? (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libnvrtc.so.11.8.89)
==1433627==    by 0x543DA8DA: ??? (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libnvrtc.so.11.8.89)
==1433627==    by 0x6: ???
==1433627==    by 0x4006439: call_init.part.0 (dl-init.c:56)
==1433627==    by 0x4006567: call_init (dl-init.c:33)
==1433627==    by 0x4006567: _dl_init (dl-init.c:117)
==1433627==    by 0x51A7AF4: _dl_catch_exception (dl-error-skeleton.c:182)
==1433627==    by 0x400DFF5: dl_open_worker (dl-open.c:808)
==1433627==    by 0x400DFF5: dl_open_worker (dl-open.c:771)
==1433627==    by 0x51A7A97: _dl_catch_exception (dl-error-skeleton.c:208)
==1433627==    by 0x400E34D: _dl_open (dl-open.c:883)
==1433627==    by 0x50C363B: dlopen_doit (dlopen.c:56)
==1433627== 
==1433627== 72,704 bytes in 1 blocks are still reachable in loss record 187 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x2DFEB79F: ??? (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libcublasLt.so.11.11.3.6)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627==    by 0x4006567: call_init (dl-init.c:33)
==1433627==    by 0x4006567: _dl_init (dl-init.c:117)
==1433627==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==1433627== 
==1433627== 72,704 bytes in 1 blocks are still reachable in loss record 188 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x27A4E50F: ??? (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libcublas.so.11.11.3.6)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627==    by 0x4006567: call_init (dl-init.c:33)
==1433627==    by 0x4006567: _dl_init (dl-init.c:117)
==1433627==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==1433627== 
==1433627== 72,704 bytes in 1 blocks are still reachable in loss record 189 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x1601503F: ??? (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libcufft.so.10.9.0.58)
==1433627==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==1433627==    by 0x4006567: call_init (dl-init.c:33)
==1433627==    by 0x4006567: _dl_init (dl-init.c:117)
==1433627==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==1433627== 
==1433627== 131,072 bytes in 1 blocks are still reachable in loss record 190 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x5277C6BF: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52672A6E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 168,960 bytes in 1 blocks are still reachable in loss record 191 of 192
==1433627==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x52696FD9: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x5290E673: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x52672E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== 655,360 bytes in 1 blocks are still reachable in loss record 192 of 192
==1433627==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==1433627==    by 0x52673075: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x526EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==1433627==    by 0x76DFE0A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76DFF07: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x50CCEE7: __pthread_once_slow (pthread_once.c:116)
==1433627==    by 0x772F8D8: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x76D6836: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x7708800: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6698FDD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x6699202: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627==    by 0x66993FA: createInferRuntime_INTERNAL (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==1433627== 
==1433627== LEAK SUMMARY:
==1433627==    definitely lost: 0 bytes in 0 blocks
==1433627==    indirectly lost: 0 bytes in 0 blocks
==1433627==      possibly lost: 25,000 bytes in 70 blocks
==1433627==    still reachable: 1,403,948 bytes in 309 blocks
==1433627==         suppressed: 0 bytes in 0 blocks
==1433627== 
==1433627== For lists of detected and suppressed errors, rerun with: -s
==1433627== ERROR SUMMARY: 18 errors from 18 contexts (suppressed: 0 from 0)
