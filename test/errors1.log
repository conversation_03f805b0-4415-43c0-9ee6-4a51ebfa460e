==1307739== Memcheck, a memory error detector
==1307739== Copyright (C) 2002-2017, and GNU GPL'd, by <PERSON> et al.
==1307739== Using Valgrind-3.18.1 and LibVEX; rerun with -h for copyright info
==1307739== Command: ./test_algorithm grep -n -A8 -B2 Invalid\\|uninitialised\\|segmentation errors.log
==1307739== Parent PID: 1263301
==1307739== 
==1307739== Warning: set address range perms: large range [0x15da6000, 0x26e81000) (noaccess)
==1307739== Warning: set address range perms: large range [0x15e00000, 0x26cdb000) (defined)
==1307739== Warning: set address range perms: large range [0x2d65e000, 0x51de4000) (noaccess)
==1307739== Warning: set address range perms: large range [0x2d800000, 0x51d86000) (defined)
==1307739== Warning: noted but unhandled ioctl 0x30000001 with no size/direction hints.
==1307739==    This could cause spurious value errors to appear.
==1307739==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1307739== Warning: noted but unhandled ioctl 0x4b with no size/direction hints.
==1307739==    This could cause spurious value errors to appear.
==1307739==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1307739== Warning: noted but unhandled ioctl 0x27 with no size/direction hints.
==1307739==    This could cause spurious value errors to appear.
==1307739==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1307739== Warning: noted but unhandled ioctl 0x25 with no size/direction hints.
==1307739==    This could cause spurious value errors to appear.
==1307739==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1307739== Warning: noted but unhandled ioctl 0x17 with no size/direction hints.
==1307739==    This could cause spurious value errors to appear.
==1307739==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1307739== Warning: set address range perms: large range [0x200000000, 0x300200000) (noaccess)
==1307739== Warning: set address range perms: large range [0x5a487000, 0x7a486000) (noaccess)
==1307739== Warning: noted but unhandled ioctl 0x19 with no size/direction hints.
==1307739==    This could cause spurious value errors to appear.
==1307739==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1307739== Warning: noted but unhandled ioctl 0x49 with no size/direction hints.
==1307739==    This could cause spurious value errors to appear.
==1307739==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1307739== Warning: noted but unhandled ioctl 0x21 with no size/direction hints.
==1307739==    This could cause spurious value errors to appear.
==1307739==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1307739== Warning: noted but unhandled ioctl 0x1b with no size/direction hints.
==1307739==    This could cause spurious value errors to appear.
==1307739==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1307739== Warning: noted but unhandled ioctl 0x44 with no size/direction hints.
==1307739==    This could cause spurious value errors to appear.
==1307739==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==1307739== 
==1307739== Process terminating with default action of signal 13 (SIGPIPE)
==1307739==    at 0x512F8BF: __libc_write (write.c:26)
==1307739==    by 0x512F8BF: write (write.c:24)
==1307739==    by 0x50A5EEC: _IO_file_write@@GLIBC_2.2.5 (fileops.c:1180)
==1307739==    by 0x50A79E0: new_do_write (fileops.c:448)
==1307739==    by 0x50A79E0: _IO_new_do_write (fileops.c:425)
==1307739==    by 0x50A79E0: _IO_do_write@@GLIBC_2.2.5 (fileops.c:422)
==1307739==    by 0x50A54D7: _IO_file_sync@@GLIBC_2.2.5 (fileops.c:798)
==1307739==    by 0x509A1A9: fflush (iofflush.c:40)
==1307739==    by 0x4F0AE45: std::ostream::flush() (in /usr/lib/x86_64-linux-gnu/libstdc++.so.6.0.30)
==1307739==    by 0x113D0C: PrintTrackingResults(TrackingResult const*, int) (test_algorithm.cpp:148)
==1307739==    by 0x114317: main::{lambda(TrackingResult const*, int)#1}::operator()(TrackingResult const*, int) const (test_algorithm.cpp:279)
==1307739==    by 0x1152E5: void std::__invoke_impl<void, main::{lambda(TrackingResult const*, int)#1}&, TrackingResult const*, int>(std::__invoke_other, main::{lambda(TrackingResult const*, int)#1}&, TrackingResult const*&&, int&&) (invoke.h:61)
==1307739==    by 0x115194: std::enable_if<is_invocable_r_v<void, main::{lambda(TrackingResult const*, int)#1}&, TrackingResult const*, int>, void>::type std::__invoke_r<void, main::{lambda(TrackingResult const*, int)#1}&, TrackingResult const*, int>(main::{lambda(TrackingResult const*, int)#1}&, TrackingResult const*&&, int&&) (invoke.h:111)
==1307739==    by 0x11505A: std::_Function_handler<void (TrackingResult const*, int), main::{lambda(TrackingResult const*, int)#1}>::_M_invoke(std::_Any_data const&, TrackingResult const*&&, int&&) (std_function.h:290)
==1307739==    by 0x4BD74AB: std::function<void (TrackingResult const*, int)>::operator()(TrackingResult const*, int) const (std_function.h:590)
==1307739== 
==1307739== HEAP SUMMARY:
==1307739==     in use at exit: 313,592,048 bytes in 155,328 blocks
==1307739==   total heap usage: 295,644 allocs, 140,316 frees, 1,249,887,798 bytes allocated
==1307739== 
==1307739== LEAK SUMMARY:
==1307739==    definitely lost: 0 bytes in 0 blocks
==1307739==    indirectly lost: 0 bytes in 0 blocks
==1307739==      possibly lost: 89,880 bytes in 897 blocks
==1307739==    still reachable: 313,502,168 bytes in 154,431 blocks
==1307739==                       of which reachable via heuristic:
==1307739==                         stdstring          : 897,183 bytes in 15,533 blocks
==1307739==         suppressed: 0 bytes in 0 blocks
==1307739== Rerun with --leak-check=full to see details of leaked memory
==1307739== 
==1307739== For lists of detected and suppressed errors, rerun with: -s
==1307739== ERROR SUMMARY: 0 errors from 0 contexts (suppressed: 0 from 0)
