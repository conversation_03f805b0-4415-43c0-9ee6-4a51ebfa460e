# 配置文件
## confi文件中的两个配置文件：
- default_config.json: 默认配置文件
- custom_config.json: 自定义配置文件，用于覆盖默认配置文件中的部分配置
- 主要修改custom_config.json中的配置：
  - engine_paths: TensorRT引擎文件路径
  - table_paths: 俯仰角查表数据文件路径
  - log_file_path: 日志文件路径
  - log_level: 日志级别

# 模型文件
## data文件夹中的两个文件：
- trt_net_fp16_v1.trt: TensorRT引擎文件
- hecha_table.csv: 俯仰角查表数据文件
- test中bin数据是demo用的模拟重排后脉压数据：3帧雷达数据

# 修改说明
- 修改了目标跟踪函数：
- demo：test/test_algorithm.cpp：
- TargetTracking(detection_results, num_detections, &tracking_results, &num_tracks,[](const TrackingResult* batch, int num_results)
            {
                PrintTrackingResults(batch, num_results);
            }
- batch是tracking_results的分开输出；