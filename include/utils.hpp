#pragma once
#include <string>
#include <vector>
#include <complex>
#include <array>
#include <eigen3/Eigen/Dense>

// 点迹跟踪算法输入结构体
struct Point {
    std::array<float,3> position;    // 三维坐标 (x, y, z)
    std::array<float,3> velocity;    // 三维速度分量 (vx, vy, vz)
    int type;                        // 目标类型
    int frame;                       // 帧数
    int label;                       // 标签（用于聚类）
};

// 跟踪结果输出
struct TrackResult {
    Eigen::Vector3f position;
    Eigen::Vector3f velocity;
    int id;
};

// 使用算法库统一的帧头结构定义
#include "libSCR_5000_Alg.hpp"
using FrameHeader = FrameHeader_Alg;

// 输出结构体定义
struct TargetInfo {
    unsigned int id;                    // 目标id
    float vx;                           // x方向速度
    float vy;                           // y方向速度
    float vz;                           // z方向速度
    float x;                            // x坐标
    float y;                            // y坐标
    float z;                            // z坐标
    float fMV;                          // 径向速度
    float fMR;                          // 径向距离
    float fMA;                          // 目标方位角
    float fME;                          // 目标俯仰角
    float fSNR;                         // 信噪比
    float fEn;                          // 峰值点能量
    float fRcs;                         // rcs
    unsigned int type;                  // 目标类型
    unsigned long long FPGATimeLog;     // fpga时间戳
    int PreShow;                        // 上位机轨迹预测值是否显示标识：1：显示， 2：不显示
};

// 查表法相关常量定义
constexpr float X_START = -3.6f;
constexpr float X_END = 3.6f;
constexpr float X_STEP = 0.1f;
// constexpr float offsets[5] = {2.5f, 7.5f, 12.5f, 20.0f, 31.25f};

std::vector<std::string> get_bin_files(const std::string& folder_path);
std::string to_string_6digits(int i);
void initializeCustomPlugins();

// 转换为复数
std::vector<std::vector<std::complex<float>>>
convertToComplex(const std::vector<float>& realImagData);

// 查表
extern std::vector<std::vector<float>> hecha_table;     // 预加载的数据表（每列是偏差曲线）
extern std::vector<float> x_axis;
extern std::vector<float> offsets;
extern std::vector<std::vector<float>> rang_limits;

void loadHechaTable_(const std::string& path);
int getLineIndex_(int row_idx);
float findXWithOffset_(int line_idx, float y_target);

// 速度换算
float calcDopplerSpeedFromIndex(int y_idx);

// GPU版本的俯仰角计算
std::vector<std::tuple<float, float, float, float, float, float, float, float, float, float, uint16_t, int, int>>
computeElevationAngles_GPU(const FrameHeader_Alg* S_head,
    const std::vector<std::complex<float>>& S_complexData,
    const std::vector<std::complex<float>>& D_complexData,
    const std::vector<std::pair<int, int>>& test_points,
    int ROWS, int COLS);

// 聚类算法
std::vector<Point> clusterDetections_DBSCAN(const std::vector<Point>& points, float eps, int minPts, int numThreads);

// 匹配算法
std::vector<std::pair<int, int>> hungarianMatch(const Eigen::MatrixXf& cost);

// 模拟数据生成函数（用于测试）
std::vector<std::tuple<float, float, float, float, float, float, float, float, float, float, uint16_t, int, int>>
generateSimulatedResults(int num_targets, int frame_number, float noise_level = 0.1f);

// 从CSV文件读取模拟数据
std::vector<Point> loadSimulationDataFromCSV(const std::string& csv_path);

// 单个 Track 转换为 TargetInfo
TargetInfo convertTrackToTargetInfo(const TrackResult & track);

// 将 tracks 向量转换为 TargetInfo 向量
std::vector<TargetInfo> convertTracksToTargetInfos(const std::vector<TrackResult>& tracks);

// 获取结构化帧数据
FrameDataView getFrameDataView(const char* head_bytes, const char* data_bytes, size_t frame_idx);

// 提取列数据段的辅助函数
ColumnSegmentData extractColumnSegmentData(
    const float* s_complexData,
    const float* d_complexData,
    int col,
    int row);