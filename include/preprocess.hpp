#pragma once
#include <vector>
#include <cstddef>
#include <memory>
#include <cufft.h>

void sample_data(const std::vector<double>& input, std::vector<double>& output, size_t sample_size);

void normalize_input_tensor_multithreaded(
    const std::vector<double>& input_tensor,
    std::vector<float>& output_tensor,
    size_t num_threads = 4
);

void sample_and_normalize(
    const float* input,
    size_t total_floats,
    std::vector<float>& output
);

// 新增：带cuFFT列方向处理的预处理函数
void sample_normalize_and_fft(
    const float* input,
    size_t total_floats,
    std::vector<float>& output
);
