#pragma once
#include <eigen3/Eigen/Dense>
#include <vector>

class KalmanFilter3D {
public:
    KalmanFilter3D(const Eigen::Vector3f& init_pos, const Eigen::Vector3f& init_vel);

    void predict();
    void update(const Eigen::Vector3f& pos, const Eigen::Vector3f& vel);
    void reset(const Eigen::Vector3f& new_pos, const Eigen::Vector3f& new_vel);

    Eigen::Vector3f getPrediction() const;
    Eigen::Vector3f getVelocity() const;

    // 新增高频预测输出(仅预测位置)
    Eigen::Vector3f predictAt(float dt) const;

    int getId() const;
    int getAge() const;
    int getTimeSinceUpdate() const;
    int getHitStreak() const;
    std::vector<Eigen::Vector3f> getHistory() const;

    void assignId();
    bool hasId() const;

private:
    Eigen::Matrix<float, 6, 1> x;
    Eigen::Matrix<float, 6, 6> F;
    Eigen::Matrix<float, 6, 6> H;
    Eigen::Matrix<float, 6, 6> Q;
    Eigen::Matrix3f R;
    Eigen::Matrix<float, 6, 6> P;

    std::vector<Eigen::Vector3f> history;

    int age;
    int time_since_update;
    int hit_streak;
    int id;
    bool id_assigned;

    static int next_id;
};