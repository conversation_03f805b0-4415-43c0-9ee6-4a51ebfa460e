{"version": "2.0.0", "tasks": [{"label": "CMake Configure", "type": "shell", "command": "cmake", "args": ["-S", ".", "-B", "build"], "options": {"env": {"TENSORRT_ROOT": "/home/<USER>/My_APP/TensorRT-8.6.1.6"}}, "group": "build", "problemMatcher": []}, {"label": "CMake Build", "type": "shell", "command": "cmake", "args": ["--build", "build"], "options": {"env": {"TENSORRT_ROOT": "/home/<USER>/My_APP/TensorRT-8.6.1.6"}}, "group": "build", "problemMatcher": ["$gcc"]}, {"type": "cppbuild", "label": "C/C++: gcc 生成活动文件", "command": "/usr/bin/gcc", "args": ["-j8", "-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}/${fileBasenameNoExtension}"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "调试器生成的任务。"}]}