{
    "configurations": [
        {
            "name": "Linux",
            "includePath": [
                "${workspaceFolder}/**",
                "/usr/include",
                "/usr/local/include/opencv4",
                // "/home/<USER>/My_APP/cnpy/cnpy",
                "/usr/local/cuda/include",
                "/home/<USER>/My_APP/TensorRT-8.6.1.6/include",
                "/home/<USER>/My_APP/TensorRT-8.6.1.6/samples/common",
                "/home/<USER>/My_APP/json-3.12.0/include"
            ],
            "defines": [],
            "compilerPath": "/usr/bin/gcc",
            "cStandard": "c17",
            "cppStandard": "gnu++17",
            "intelliSenseMode": "linux-gcc-x64"
        }
    ],
    "version": 4
}