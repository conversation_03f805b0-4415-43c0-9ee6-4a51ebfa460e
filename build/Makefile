# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/My_Project/MSHNet_TensorRT_Test

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/My_Project/MSHNet_TensorRT_Test/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles /home/<USER>/My_Project/MSHNet_TensorRT_Test/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named SCR_5000_AI

# Build rule for target.
SCR_5000_AI: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 SCR_5000_AI
.PHONY : SCR_5000_AI

# fast build rule for target.
SCR_5000_AI/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/build
.PHONY : SCR_5000_AI/fast

#=============================================================================
# Target rules for targets named test_algorithm

# Build rule for target.
test_algorithm: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_algorithm
.PHONY : test_algorithm

# fast build rule for target.
test_algorithm/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_algorithm.dir/build.make CMakeFiles/test_algorithm.dir/build
.PHONY : test_algorithm/fast

src/KalmanFilter3D.o: src/KalmanFilter3D.cpp.o
.PHONY : src/KalmanFilter3D.o

# target to build an object file
src/KalmanFilter3D.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/KalmanFilter3D.cpp.o
.PHONY : src/KalmanFilter3D.cpp.o

src/KalmanFilter3D.i: src/KalmanFilter3D.cpp.i
.PHONY : src/KalmanFilter3D.i

# target to preprocess a source file
src/KalmanFilter3D.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/KalmanFilter3D.cpp.i
.PHONY : src/KalmanFilter3D.cpp.i

src/KalmanFilter3D.s: src/KalmanFilter3D.cpp.s
.PHONY : src/KalmanFilter3D.s

# target to generate assembly for a file
src/KalmanFilter3D.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/KalmanFilter3D.cpp.s
.PHONY : src/KalmanFilter3D.cpp.s

src/PointTracker.o: src/PointTracker.cpp.o
.PHONY : src/PointTracker.o

# target to build an object file
src/PointTracker.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/PointTracker.cpp.o
.PHONY : src/PointTracker.cpp.o

src/PointTracker.i: src/PointTracker.cpp.i
.PHONY : src/PointTracker.i

# target to preprocess a source file
src/PointTracker.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/PointTracker.cpp.i
.PHONY : src/PointTracker.cpp.i

src/PointTracker.s: src/PointTracker.cpp.s
.PHONY : src/PointTracker.s

# target to generate assembly for a file
src/PointTracker.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/PointTracker.cpp.s
.PHONY : src/PointTracker.cpp.s

src/config.o: src/config.cpp.o
.PHONY : src/config.o

# target to build an object file
src/config.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/config.cpp.o
.PHONY : src/config.cpp.o

src/config.i: src/config.cpp.i
.PHONY : src/config.i

# target to preprocess a source file
src/config.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/config.cpp.i
.PHONY : src/config.cpp.i

src/config.s: src/config.cpp.s
.PHONY : src/config.s

# target to generate assembly for a file
src/config.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/config.cpp.s
.PHONY : src/config.cpp.s

src/fft_gpu.o: src/fft_gpu.cpp.o
.PHONY : src/fft_gpu.o

# target to build an object file
src/fft_gpu.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/fft_gpu.cpp.o
.PHONY : src/fft_gpu.cpp.o

src/fft_gpu.i: src/fft_gpu.cpp.i
.PHONY : src/fft_gpu.i

# target to preprocess a source file
src/fft_gpu.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/fft_gpu.cpp.i
.PHONY : src/fft_gpu.cpp.i

src/fft_gpu.s: src/fft_gpu.cpp.s
.PHONY : src/fft_gpu.s

# target to generate assembly for a file
src/fft_gpu.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/fft_gpu.cpp.s
.PHONY : src/fft_gpu.cpp.s

src/infer_engine.o: src/infer_engine.cpp.o
.PHONY : src/infer_engine.o

# target to build an object file
src/infer_engine.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/infer_engine.cpp.o
.PHONY : src/infer_engine.cpp.o

src/infer_engine.i: src/infer_engine.cpp.i
.PHONY : src/infer_engine.i

# target to preprocess a source file
src/infer_engine.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/infer_engine.cpp.i
.PHONY : src/infer_engine.cpp.i

src/infer_engine.s: src/infer_engine.cpp.s
.PHONY : src/infer_engine.s

# target to generate assembly for a file
src/infer_engine.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/infer_engine.cpp.s
.PHONY : src/infer_engine.cpp.s

src/libSCR_5000_Alg.o: src/libSCR_5000_Alg.cpp.o
.PHONY : src/libSCR_5000_Alg.o

# target to build an object file
src/libSCR_5000_Alg.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/libSCR_5000_Alg.cpp.o
.PHONY : src/libSCR_5000_Alg.cpp.o

src/libSCR_5000_Alg.i: src/libSCR_5000_Alg.cpp.i
.PHONY : src/libSCR_5000_Alg.i

# target to preprocess a source file
src/libSCR_5000_Alg.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/libSCR_5000_Alg.cpp.i
.PHONY : src/libSCR_5000_Alg.cpp.i

src/libSCR_5000_Alg.s: src/libSCR_5000_Alg.cpp.s
.PHONY : src/libSCR_5000_Alg.s

# target to generate assembly for a file
src/libSCR_5000_Alg.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/libSCR_5000_Alg.cpp.s
.PHONY : src/libSCR_5000_Alg.cpp.s

src/logger.o: src/logger.cpp.o
.PHONY : src/logger.o

# target to build an object file
src/logger.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/logger.cpp.o
.PHONY : src/logger.cpp.o

src/logger.i: src/logger.cpp.i
.PHONY : src/logger.i

# target to preprocess a source file
src/logger.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/logger.cpp.i
.PHONY : src/logger.cpp.i

src/logger.s: src/logger.cpp.s
.PHONY : src/logger.s

# target to generate assembly for a file
src/logger.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/logger.cpp.s
.PHONY : src/logger.cpp.s

src/postprocess.o: src/postprocess.cpp.o
.PHONY : src/postprocess.o

# target to build an object file
src/postprocess.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/postprocess.cpp.o
.PHONY : src/postprocess.cpp.o

src/postprocess.i: src/postprocess.cpp.i
.PHONY : src/postprocess.i

# target to preprocess a source file
src/postprocess.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/postprocess.cpp.i
.PHONY : src/postprocess.cpp.i

src/postprocess.s: src/postprocess.cpp.s
.PHONY : src/postprocess.s

# target to generate assembly for a file
src/postprocess.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/postprocess.cpp.s
.PHONY : src/postprocess.cpp.s

src/preprocess.o: src/preprocess.cpp.o
.PHONY : src/preprocess.o

# target to build an object file
src/preprocess.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/preprocess.cpp.o
.PHONY : src/preprocess.cpp.o

src/preprocess.i: src/preprocess.cpp.i
.PHONY : src/preprocess.i

# target to preprocess a source file
src/preprocess.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/preprocess.cpp.i
.PHONY : src/preprocess.cpp.i

src/preprocess.s: src/preprocess.cpp.s
.PHONY : src/preprocess.s

# target to generate assembly for a file
src/preprocess.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/preprocess.cpp.s
.PHONY : src/preprocess.cpp.s

src/utils.o: src/utils.cpp.o
.PHONY : src/utils.o

# target to build an object file
src/utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/utils.cpp.o
.PHONY : src/utils.cpp.o

src/utils.i: src/utils.cpp.i
.PHONY : src/utils.i

# target to preprocess a source file
src/utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/utils.cpp.i
.PHONY : src/utils.cpp.i

src/utils.s: src/utils.cpp.s
.PHONY : src/utils.s

# target to generate assembly for a file
src/utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SCR_5000_AI.dir/build.make CMakeFiles/SCR_5000_AI.dir/src/utils.cpp.s
.PHONY : src/utils.cpp.s

test/test_algorithm.o: test/test_algorithm.cpp.o
.PHONY : test/test_algorithm.o

# target to build an object file
test/test_algorithm.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_algorithm.dir/build.make CMakeFiles/test_algorithm.dir/test/test_algorithm.cpp.o
.PHONY : test/test_algorithm.cpp.o

test/test_algorithm.i: test/test_algorithm.cpp.i
.PHONY : test/test_algorithm.i

# target to preprocess a source file
test/test_algorithm.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_algorithm.dir/build.make CMakeFiles/test_algorithm.dir/test/test_algorithm.cpp.i
.PHONY : test/test_algorithm.cpp.i

test/test_algorithm.s: test/test_algorithm.cpp.s
.PHONY : test/test_algorithm.s

# target to generate assembly for a file
test/test_algorithm.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_algorithm.dir/build.make CMakeFiles/test_algorithm.dir/test/test_algorithm.cpp.s
.PHONY : test/test_algorithm.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... SCR_5000_AI"
	@echo "... test_algorithm"
	@echo "... src/KalmanFilter3D.o"
	@echo "... src/KalmanFilter3D.i"
	@echo "... src/KalmanFilter3D.s"
	@echo "... src/PointTracker.o"
	@echo "... src/PointTracker.i"
	@echo "... src/PointTracker.s"
	@echo "... src/config.o"
	@echo "... src/config.i"
	@echo "... src/config.s"
	@echo "... src/fft_gpu.o"
	@echo "... src/fft_gpu.i"
	@echo "... src/fft_gpu.s"
	@echo "... src/infer_engine.o"
	@echo "... src/infer_engine.i"
	@echo "... src/infer_engine.s"
	@echo "... src/libSCR_5000_Alg.o"
	@echo "... src/libSCR_5000_Alg.i"
	@echo "... src/libSCR_5000_Alg.s"
	@echo "... src/logger.o"
	@echo "... src/logger.i"
	@echo "... src/logger.s"
	@echo "... src/postprocess.o"
	@echo "... src/postprocess.i"
	@echo "... src/postprocess.s"
	@echo "... src/preprocess.o"
	@echo "... src/preprocess.i"
	@echo "... src/preprocess.s"
	@echo "... src/utils.o"
	@echo "... src/utils.i"
	@echo "... src/utils.s"
	@echo "... test/test_algorithm.o"
	@echo "... test/test_algorithm.i"
	@echo "... test/test_algorithm.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

